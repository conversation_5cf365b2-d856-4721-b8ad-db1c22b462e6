# UI/UX 设计指南

## 设计理念

### 核心原则

1. **极致简单** - 零学习成本，直觉式操作
2. **性能优先** - 60fps 流畅体验，即时反馈
3. **美学至上** - 世界顶尖的视觉设计
4. **无障碍友好** - 支持各种辅助功能

### 设计语言

- **简约现代** - 干净的线条，充足的留白
- **色彩丰富** - 渐变色彩，动态主题
- **动效流畅** - 自然的过渡动画
- **手势友好** - 符合平台习惯的交互

## 视觉系统

### 色彩系统

```dart
class AppColors {
  // 主色调 - 渐变蓝紫色
  static const primaryGradient = LinearGradient(
    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // 辅助色彩
  static const secondary = Color(0xFF6C5CE7);
  static const accent = Color(0xFFFF6B6B);
  static const success = Color(0xFF00B894);
  static const warning = Color(0xFFFFAB00);
  static const error = Color(0xFFE17055);
  
  // 中性色
  static const surface = Color(0xFFFAFAFA);
  static const background = Color(0xFFFFFFFF);
  static const onSurface = Color(0xFF1A1A1A);
  static const onBackground = Color(0xFF2D3436);
  
  // 暗色主题
  static const darkSurface = Color(0xFF1A1A1A);
  static const darkBackground = Color(0xFF121212);
  static const darkOnSurface = Color(0xFFE0E0E0);
  static const darkOnBackground = Color(0xFFFFFFFF);
}
```

### 字体系统

```dart
class AppTextStyles {
  // 标题字体
  static const headline1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.w700,
    letterSpacing: -0.5,
    height: 1.2,
  );
  
  static const headline2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    letterSpacing: -0.25,
    height: 1.3,
  );
  
  // 正文字体
  static const body1 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.15,
    height: 1.5,
  );
  
  static const body2 = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
    height: 1.4,
  );
  
  // 按钮字体
  static const button = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    letterSpacing: 1.25,
  );
}
```

### 间距系统

```dart
class AppSpacing {
  static const xs = 4.0;
  static const sm = 8.0;
  static const md = 16.0;
  static const lg = 24.0;
  static const xl = 32.0;
  static const xxl = 48.0;
  static const xxxl = 64.0;
}
```

## 组件设计

### 按钮系统

```dart
class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final AppButtonType type;
  final AppButtonSize size;
  
  const AppButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.type = AppButtonType.primary,
    this.size = AppButtonSize.medium,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      child: Material(
        borderRadius: BorderRadius.circular(_getBorderRadius()),
        child: InkWell(
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          onTap: onPressed,
          child: Container(
            padding: _getPadding(),
            decoration: _getDecoration(),
            child: Text(
              text,
              style: _getTextStyle(),
            ),
          ),
        ),
      ),
    );
  }
}
```

### 卡片组件

```dart
class AppCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final double? elevation;
  final Color? backgroundColor;
  
  const AppCard({
    Key? key,
    required this.child,
    this.padding,
    this.elevation,
    this.backgroundColor,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: elevation ?? 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }
}
```

## 交互设计

### 手势系统

```dart
class GestureHandler {
  // 图片编辑手势
  static void setupImageEditingGestures(Widget imageWidget) {
    return GestureDetector(
      // 缩放手势
      onScaleStart: (details) => _handleScaleStart(details),
      onScaleUpdate: (details) => _handleScaleUpdate(details),
      onScaleEnd: (details) => _handleScaleEnd(details),
      
      // 双击手势
      onDoubleTap: () => _handleDoubleTap(),
      
      // 长按手势
      onLongPress: () => _handleLongPress(),
      
      child: imageWidget,
    );
  }
  
  // 拼图拖拽手势
  static Widget buildDraggableImage(ImageData imageData) {
    return Draggable<ImageData>(
      data: imageData,
      feedback: _buildDragFeedback(imageData),
      childWhenDragging: _buildDragPlaceholder(),
      child: _buildImageTile(imageData),
    );
  }
}
```

### 动画系统

```dart
class AppAnimations {
  // 页面转场动画
  static Route<T> createRoute<T>(Widget page) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;
        
        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );
        
        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }
  
  // 滤镜预览动画
  static Widget buildFilterPreview(FilterType filter) {
    return AnimatedBuilder(
      animation: _filterAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 + (_filterAnimation.value * 0.1),
          child: Opacity(
            opacity: 0.7 + (_filterAnimation.value * 0.3),
            child: child,
          ),
        );
      },
      child: FilterPreviewWidget(filter: filter),
    );
  }
}
```

## 响应式设计

### 断点系统

```dart
class AppBreakpoints {
  static const mobile = 600;
  static const tablet = 900;
  static const desktop = 1200;
  static const largeDesktop = 1800;
  
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < desktop;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }
}
```

### 自适应布局

```dart
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  
  const ResponsiveLayout({
    Key? key,
    required this.mobile,
    this.tablet,
    this.desktop,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= AppBreakpoints.desktop) {
          return desktop ?? tablet ?? mobile;
        } else if (constraints.maxWidth >= AppBreakpoints.mobile) {
          return tablet ?? mobile;
        } else {
          return mobile;
        }
      },
    );
  }
}
```

## 无障碍设计

### 语义化标签

```dart
class AccessibleWidget extends StatelessWidget {
  final Widget child;
  final String semanticLabel;
  final String? semanticHint;
  final bool excludeSemantics;
  
  const AccessibleWidget({
    Key? key,
    required this.child,
    required this.semanticLabel,
    this.semanticHint,
    this.excludeSemantics = false,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      excludeSemantics: excludeSemantics,
      child: child,
    );
  }
}
```

### 对比度检查

```dart
class ContrastChecker {
  static bool hasGoodContrast(Color foreground, Color background) {
    final ratio = _calculateContrastRatio(foreground, background);
    return ratio >= 4.5; // WCAG AA 标准
  }
  
  static double _calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = _getLuminance(color1);
    final luminance2 = _getLuminance(color2);
    
    final lighter = math.max(luminance1, luminance2);
    final darker = math.min(luminance1, luminance2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }
}
```

## 性能优化

### 图片优化

```dart
class OptimizedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  
  const OptimizedImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return _buildLoadingPlaceholder();
      },
      errorBuilder: (context, error, stackTrace) {
        return _buildErrorPlaceholder();
      },
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
    );
  }
}
```

### 列表优化

```dart
class OptimizedListView extends StatelessWidget {
  final List<Widget> children;
  final ScrollController? controller;

  const OptimizedListView({
    Key? key,
    required this.children,
    this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: controller,
      itemCount: children.length,
      itemBuilder: (context, index) {
        return AutomaticKeepAliveClientMixin(
          child: children[index],
        );
      },
      cacheExtent: 1000, // 预加载范围
      addAutomaticKeepAlives: true,
      addRepaintBoundaries: true,
    );
  }
}
```

## 主题系统

### 动态主题

```dart
class AppTheme {
  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.light,
    ),
    textTheme: _buildTextTheme(),
    elevatedButtonTheme: _buildElevatedButtonTheme(),
    cardTheme: _buildCardTheme(),
  );

  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.dark,
    ),
    textTheme: _buildTextTheme(),
    elevatedButtonTheme: _buildElevatedButtonTheme(),
    cardTheme: _buildCardTheme(),
  );
}
```
```
