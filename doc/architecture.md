# 技术架构文档

## 整体架构

```mermaid
graph TB
    subgraph "Flutter Layer"
        UI[UI Components]
        State[State Management]
        Router[Navigation]
        I18n[Internationalization]
    end
    
    subgraph "FFI Bridge"
        Bridge[flutter_rust_bridge]
    end
    
    subgraph "Rust Core"
        ImageProc[Image Processing]
        Filters[Filter Engine]
        Collage[Collage Engine]
        Convert[Format Converter]
        LivePhoto[Live Photo Engine]
    end
    
    subgraph "Platform"
        Android[Android]
        iOS[iOS]
        macOS[macOS]
        Windows[Windows]
    end
    
    UI --> State
    State --> Bridge
    Bridge --> ImageProc
    Bridge --> Filters
    Bridge --> Collage
    Bridge --> Convert
    Bridge --> LivePhoto
    
    ImageProc --> Platform
    Filters --> Platform
    Collage --> Platform
    Convert --> Platform
    LivePhoto --> Platform
```

## Flutter 架构

### 状态管理策略

我们采用轻量级的状态管理方案，避免使用代码生成库：

```dart
// 使用 ValueNotifier 进行简单状态管理
class ImageEditingController extends ChangeNotifier {
  ValueNotifier<ImageState> _imageState = ValueNotifier(ImageState.initial());
  
  ValueNotifier<ImageState> get imageState => _imageState;
  
  Future<void> applyFilter(FilterType filter) async {
    // 调用 Rust 层处理
    final result = await RustImageProcessor.applyFilter(
      _imageState.value.imageData,
      filter,
    );
    
    _imageState.value = _imageState.value.copyWith(
      imageData: result,
      history: [..._imageState.value.history, filter],
    );
    notifyListeners();
  }
}
```

### 目录结构

```
lib/
├── main.dart                   # 应用入口
├── app/                        # 应用配置
│   ├── app.dart               # 主应用
│   ├── router.dart            # 路由配置
│   └── theme.dart             # 主题配置
├── core/                       # 核心功能
│   ├── constants/             # 常量定义
│   ├── extensions/            # 扩展方法
│   ├── utils/                 # 工具类
│   └── services/              # 服务层
├── features/                   # 功能模块
│   ├── home/                  # 首页
│   ├── editor/                # 图片编辑
│   ├── collage/               # 拼图
│   ├── filters/               # 滤镜
│   ├── converter/             # 格式转换
│   └── live_photo/            # 实况图片
├── shared/                     # 共享组件
│   ├── widgets/               # UI 组件
│   ├── models/                # 数据模型
│   └── controllers/           # 控制器
└── l10n/                      # 国际化
    ├── app_en.arb
    ├── app_zh.arb
    ├── app_zh_TW.arb
    ├── app_ja.arb
    └── app_ko.arb
```

## Rust 架构

### 核心库结构

```
rust/
├── Cargo.toml                 # 项目配置
├── src/
│   ├── lib.rs                 # 库入口
│   ├── bridge.rs              # FFI 桥接
│   ├── core/                  # 核心功能
│   │   ├── mod.rs
│   │   ├── image_processor.rs # 图像处理器
│   │   ├── filter_engine.rs   # 滤镜引擎
│   │   ├── collage_engine.rs  # 拼图引擎
│   │   ├── format_converter.rs# 格式转换器
│   │   └── live_photo.rs      # 实况图片
│   ├── filters/               # 滤镜实现
│   │   ├── mod.rs
│   │   ├── portrait.rs        # 人像滤镜
│   │   ├── landscape.rs       # 风景滤镜
│   │   ├── architecture.rs    # 建筑滤镜
│   │   └── food.rs            # 美食滤镜
│   ├── utils/                 # 工具函数
│   │   ├── mod.rs
│   │   ├── color.rs           # 颜色处理
│   │   ├── geometry.rs        # 几何计算
│   │   └── performance.rs     # 性能监控
│   └── error.rs               # 错误处理
├── tests/                     # 测试文件
└── benches/                   # 性能测试
```

### 性能优化策略

1. **并行处理**: 使用 `rayon` 进行 CPU 密集型任务的并行化
2. **内存管理**: 零拷贝操作，减少内存分配
3. **SIMD 优化**: 利用现代 CPU 的向量指令
4. **缓存策略**: 智能缓存常用滤镜和处理结果

```rust
use rayon::prelude::*;

pub fn apply_filter_parallel(
    image: &ImageBuffer<Rgb<u8>, Vec<u8>>,
    filter: &dyn Filter,
) -> ImageBuffer<Rgb<u8>, Vec<u8>> {
    let (width, height) = image.dimensions();
    let mut output = ImageBuffer::new(width, height);
    
    output
        .enumerate_pixels_mut()
        .par_bridge()
        .for_each(|(x, y, pixel)| {
            let input_pixel = image.get_pixel(x, y);
            *pixel = filter.apply(input_pixel, x, y);
        });
    
    output
}
```

## FFI 集成

### 数据传输优化

使用高效的数据传输格式，减少序列化开销：

```rust
#[flutter_rust_bridge::frb(sync)]
pub struct ImageData {
    pub width: u32,
    pub height: u32,
    pub data: Vec<u8>,
    pub format: ImageFormat,
}

#[flutter_rust_bridge::frb(sync)]
pub enum ImageFormat {
    Rgb8,
    Rgba8,
    Gray8,
}
```

### 异步处理

对于耗时操作，使用异步接口避免阻塞 UI：

```rust
#[flutter_rust_bridge::frb]
pub async fn process_image_async(
    image_data: ImageData,
    operations: Vec<ImageOperation>,
) -> Result<ImageData, ProcessingError> {
    tokio::task::spawn_blocking(move || {
        let mut processor = ImageProcessor::new(image_data);
        for operation in operations {
            processor.apply_operation(operation)?;
        }
        Ok(processor.get_result())
    }).await?
}
```

## 跨平台适配

### 平台特定优化

- **Android**: 利用 NDK 和 Vulkan API
- **iOS**: 集成 Metal Performance Shaders
- **macOS**: 使用 Core Image 加速
- **Windows**: DirectX 计算着色器支持

### 构建配置

```toml
[target.'cfg(target_os = "android")'.dependencies]
ndk = "0.7"
vulkan = "0.1"

[target.'cfg(target_os = "ios")'.dependencies]
metal = "0.24"
core-graphics = "0.23"

[target.'cfg(target_os = "macos")'.dependencies]
core-image = "0.1"
metal = "0.24"

[target.'cfg(target_os = "windows")'.dependencies]
winapi = "0.3"
directx = "0.1"
```

## 性能监控

### 关键指标

1. **处理延迟**: 图像处理操作的响应时间
2. **内存使用**: 峰值内存占用和内存泄漏检测
3. **CPU 利用率**: 多核 CPU 的利用效率
4. **电池消耗**: 移动设备的功耗优化

### 监控实现

```rust
pub struct PerformanceMonitor {
    start_time: Instant,
    memory_tracker: MemoryTracker,
}

impl PerformanceMonitor {
    pub fn measure<T, F>(&mut self, operation: F) -> (T, Duration)
    where
        F: FnOnce() -> T,
    {
        let start = Instant::now();
        let result = operation();
        let duration = start.elapsed();
        
        self.log_performance("operation", duration);
        (result, duration)
    }
}
```
