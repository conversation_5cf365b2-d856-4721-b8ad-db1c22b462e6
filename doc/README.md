# 图神 (<PERSON><PERSON><PERSON>) - 专业图像处理应用

## 项目概述

图神是一款基于 Flutter + Rust 开发的专业图像处理应用，专注于为摄影爱好者和内容创作者提供极致简单的交互体验和极高的性能表现。

## 目标用户

- 📸 摄影爱好者和分享达人
- 🎨 内容创作者和设计师
- 📱 追求高品质图像处理的用户

## 核心特性

### 🚀 性能与体验
- **极致简单的交互** - 直观的手势操作，零学习成本
- **极高的性能** - Rust 驱动的图像处理引擎
- **跨平台支持** - Android、iOS、macOS、Windows 全平台覆盖

### 🌍 国际化支持
- 简体中文 (zh-CN)
- 繁体中文 (zh-TW)
- English (en)
- 日本語 (ja)
- 한국어 (ko)

### 🎯 核心功能
1. **智能拼图** - 多种布局，自动优化
2. **专业图片编辑** - 裁剪、调色、特效
3. **实况图片转换** - 图片/视频转 Live Photo
4. **格式转换** - 支持所有主流图像格式
5. **AI 滤镜系统** - 人像、风景、建筑、美食等专业滤镜
6. **批量处理** - 高效的批量操作
7. **云端同步** - 跨设备无缝体验
8. **社区分享** - 作品展示与交流

## 技术架构

### 前端 - Flutter
- **状态管理**: 原生 ValueNotifier + ChangeNotifier
- **路由**: GoRouter
- **国际化**: flutter_localizations + intl
- **UI框架**: 自研组件库 + Material Design 3

### 后端 - Rust
- **图像处理**: image, imageproc, photon
- **性能优化**: rayon (并行处理)
- **FFI**: flutter_rust_bridge
- **跨平台**: uniffi

### 架构原则
- 🎯 **性能优先** - 所有图像处理在 Rust 层完成
- 🔧 **易于扩展** - 模块化设计，插件化架构
- 🛡️ **类型安全** - 强类型系统，减少运行时错误
- 📱 **响应式设计** - 适配各种屏幕尺寸

## 项目结构

```
tushen/
├── doc/                    # 项目文档
├── lib/                    # Flutter 应用代码
├── rust/                   # Rust 图像处理库
├── assets/                 # 资源文件
├── test/                   # 测试文件
└── tools/                  # 开发工具
```

## 开发指南

详细的开发指南请参考：
- [技术架构文档](./architecture.md)
- [UI/UX 设计指南](./ui-ux-guide.md)
- [Rust 开发指南](./rust-development.md)
- [Flutter 开发指南](./flutter-development.md)
- [国际化指南](./internationalization.md)

## 版本规划

### v1.0 - 核心功能
- 基础图片编辑
- 拼图功能
- 格式转换
- 基础滤镜

### v1.1 - 增强体验
- 实况图片转换
- AI 滤镜
- 批量处理
- 性能优化

### v1.2 - 社交功能
- 云端同步
- 社区分享
- 模板市场
- 协作编辑

## 许可证

MIT License - 详见 [LICENSE](../LICENSE) 文件
