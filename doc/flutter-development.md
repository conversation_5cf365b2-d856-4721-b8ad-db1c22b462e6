# Flutter 开发指南

## 项目架构

### 状态管理策略

我们采用轻量级的状态管理方案，避免使用 bloc、riverpod 等代码生成库：

```dart
// 基础状态管理
abstract class AppController extends ChangeNotifier {
  bool _isLoading = false;
  String? _error;
  
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void setError(String? error) {
    _error = error;
    notifyListeners();
  }
  
  void clearError() {
    _error = null;
    notifyListeners();
  }
}

// 图片编辑控制器
class ImageEditingController extends AppController {
  final ValueNotifier<ImageState> _imageState = ValueNotifier(ImageState.initial());
  final List<EditingStep> _history = [];
  int _currentStep = -1;
  
  ValueNotifier<ImageState> get imageState => _imageState;
  bool get canUndo => _currentStep >= 0;
  bool get canRedo => _currentStep < _history.length - 1;
  
  Future<void> applyFilter(FilterType filter) async {
    try {
      setLoading(true);
      clearError();
      
      final result = await RustImageProcessor.applyFilter(
        _imageState.value.imageData,
        filter,
      );
      
      _addToHistory(EditingStep.filter(filter));
      _imageState.value = _imageState.value.copyWith(
        imageData: result,
        appliedFilters: [..._imageState.value.appliedFilters, filter],
      );
      
    } catch (e) {
      setError('Failed to apply filter: $e');
    } finally {
      setLoading(false);
    }
  }
  
  void undo() {
    if (canUndo) {
      _currentStep--;
      _rebuildImageFromHistory();
    }
  }
  
  void redo() {
    if (canRedo) {
      _currentStep++;
      _rebuildImageFromHistory();
    }
  }
  
  void _addToHistory(EditingStep step) {
    // 清除当前步骤之后的历史
    if (_currentStep < _history.length - 1) {
      _history.removeRange(_currentStep + 1, _history.length);
    }
    
    _history.add(step);
    _currentStep = _history.length - 1;
    
    // 限制历史记录数量
    if (_history.length > 50) {
      _history.removeAt(0);
      _currentStep--;
    }
  }
  
  Future<void> _rebuildImageFromHistory() async {
    // 从原始图片重新应用所有操作
    setLoading(true);
    try {
      var currentImage = _imageState.value.originalImageData;
      
      for (int i = 0; i <= _currentStep; i++) {
        currentImage = await _applyStep(_history[i], currentImage);
      }
      
      _imageState.value = _imageState.value.copyWith(
        imageData: currentImage,
      );
    } finally {
      setLoading(false);
    }
  }
}
```

### 路由配置

使用 GoRouter 进行声明式路由管理：

```dart
final appRouter = GoRouter(
  initialLocation: '/home',
  routes: [
    GoRoute(
      path: '/home',
      name: 'home',
      builder: (context, state) => const HomeScreen(),
    ),
    GoRoute(
      path: '/editor',
      name: 'editor',
      builder: (context, state) {
        final imageData = state.extra as Uint8List?;
        return ImageEditorScreen(imageData: imageData);
      },
    ),
    GoRoute(
      path: '/collage',
      name: 'collage',
      builder: (context, state) {
        final images = state.extra as List<Uint8List>?;
        return CollageScreen(images: images);
      },
    ),
    GoRoute(
      path: '/filters',
      name: 'filters',
      builder: (context, state) {
        final imageData = state.extra as Uint8List?;
        return FiltersScreen(imageData: imageData);
      },
    ),
    GoRoute(
      path: '/converter',
      name: 'converter',
      builder: (context, state) => const FormatConverterScreen(),
    ),
    GoRoute(
      path: '/settings',
      name: 'settings',
      builder: (context, state) => const SettingsScreen(),
    ),
  ],
  errorBuilder: (context, state) => ErrorScreen(error: state.error),
);
```

### 服务层架构

```dart
// 抽象服务接口
abstract class ImageProcessingService {
  Future<Uint8List> applyFilter(Uint8List imageData, FilterType filter);
  Future<Uint8List> createCollage(List<Uint8List> images, CollageLayout layout);
  Future<Uint8List> convertFormat(Uint8List imageData, ImageFormat format);
  Future<Uint8List> createLivePhoto(Uint8List imageData, Uint8List videoData);
}

// Rust FFI 实现
class RustImageProcessingService implements ImageProcessingService {
  @override
  Future<Uint8List> applyFilter(Uint8List imageData, FilterType filter) async {
    try {
      final result = await RustApi.applyFilter(
        imageData: imageData,
        filterType: filter.toRustEnum(),
        intensity: filter.intensity,
      );
      return result;
    } catch (e) {
      throw ImageProcessingException('Failed to apply filter: $e');
    }
  }
  
  @override
  Future<Uint8List> createCollage(List<Uint8List> images, CollageLayout layout) async {
    try {
      final result = await RustApi.createCollage(
        images: images,
        layoutType: layout.type.toRustEnum(),
        canvasWidth: layout.width,
        canvasHeight: layout.height,
      );
      return result;
    } catch (e) {
      throw ImageProcessingException('Failed to create collage: $e');
    }
  }
}

// 服务定位器
class ServiceLocator {
  static final _instance = ServiceLocator._internal();
  factory ServiceLocator() => _instance;
  ServiceLocator._internal();
  
  final Map<Type, dynamic> _services = {};
  
  void register<T>(T service) {
    _services[T] = service;
  }
  
  T get<T>() {
    final service = _services[T];
    if (service == null) {
      throw Exception('Service of type $T not registered');
    }
    return service as T;
  }
}

// 服务初始化
void setupServices() {
  final serviceLocator = ServiceLocator();
  
  serviceLocator.register<ImageProcessingService>(
    RustImageProcessingService(),
  );
  serviceLocator.register<StorageService>(
    LocalStorageService(),
  );
  serviceLocator.register<AnalyticsService>(
    FirebaseAnalyticsService(),
  );
}
```

## 核心功能实现

### 图片编辑器

```dart
class ImageEditorScreen extends StatefulWidget {
  final Uint8List? imageData;
  
  const ImageEditorScreen({Key? key, this.imageData}) : super(key: key);
  
  @override
  State<ImageEditorScreen> createState() => _ImageEditorScreenState();
}

class _ImageEditorScreenState extends State<ImageEditorScreen>
    with TickerProviderStateMixin {
  late final ImageEditingController _controller;
  late final AnimationController _toolbarAnimationController;
  late final Animation<double> _toolbarAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = ImageEditingController();
    
    _toolbarAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _toolbarAnimation = CurvedAnimation(
      parent: _toolbarAnimationController,
      curve: Curves.easeInOut,
    );
    
    if (widget.imageData != null) {
      _controller.loadImage(widget.imageData!);
    }
    
    _toolbarAnimationController.forward();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            _buildTopToolbar(),
            Expanded(
              child: _buildImageCanvas(),
            ),
            _buildBottomToolbar(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildImageCanvas() {
    return ValueListenableBuilder<ImageState>(
      valueListenable: _controller.imageState,
      builder: (context, imageState, child) {
        if (imageState.imageData == null) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        
        return InteractiveViewer(
          minScale: 0.5,
          maxScale: 5.0,
          child: GestureDetector(
            onDoubleTap: _handleDoubleTap,
            onLongPress: _handleLongPress,
            child: Image.memory(
              imageState.imageData!,
              fit: BoxFit.contain,
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildBottomToolbar() {
    return AnimatedBuilder(
      animation: _toolbarAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 100 * (1 - _toolbarAnimation.value)),
          child: Container(
            height: 120,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildToolButton(
                  icon: Icons.tune,
                  label: 'Adjust',
                  onTap: () => _showAdjustmentPanel(),
                ),
                _buildToolButton(
                  icon: Icons.filter,
                  label: 'Filters',
                  onTap: () => _showFiltersPanel(),
                ),
                _buildToolButton(
                  icon: Icons.crop,
                  label: 'Crop',
                  onTap: () => _enterCropMode(),
                ),
                _buildToolButton(
                  icon: Icons.text_fields,
                  label: 'Text',
                  onTap: () => _addText(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildToolButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
```

### 滤镜选择器

```dart
class FiltersPanel extends StatefulWidget {
  final ImageEditingController controller;
  
  const FiltersPanel({Key? key, required this.controller}) : super(key: key);
  
  @override
  State<FiltersPanel> createState() => _FiltersPanelState();
}

class _FiltersPanelState extends State<FiltersPanel> {
  final List<FilterCategory> _categories = [
    FilterCategory.portrait,
    FilterCategory.landscape,
    FilterCategory.architecture,
    FilterCategory.food,
    FilterCategory.artistic,
  ];
  
  FilterCategory _selectedCategory = FilterCategory.portrait;
  
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      decoration: const BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          _buildCategoryTabs(),
          Expanded(
            child: _buildFilterGrid(),
          ),
        ],
      ),
    );
  }
  
  Widget _buildCategoryTabs() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = category == _selectedCategory;
          
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedCategory = category;
              });
            },
            child: Container(
              margin: const EdgeInsets.only(right: 16),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: isSelected ? Colors.blue : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected ? Colors.blue : Colors.grey,
                ),
              ),
              child: Text(
                category.displayName,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.grey,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
  
  Widget _buildFilterGrid() {
    final filters = FilterRegistry.getFiltersForCategory(_selectedCategory);
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: filters.length,
      itemBuilder: (context, index) {
        final filter = filters[index];
        
        return GestureDetector(
          onTap: () => _applyFilter(filter),
          child: Column(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.withOpacity(0.3)),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: FilterPreview(
                      filter: filter,
                      imageData: widget.controller.imageState.value.imageData,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                filter.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }
  
  void _applyFilter(FilterType filter) {
    widget.controller.applyFilter(filter);
    Navigator.of(context).pop();
  }
}
```

## 性能优化

### 图片缓存

```dart
class ImageCacheManager {
  static final _instance = ImageCacheManager._internal();
  factory ImageCacheManager() => _instance;
  ImageCacheManager._internal();
  
  final Map<String, Uint8List> _memoryCache = {};
  final int _maxCacheSize = 100 * 1024 * 1024; // 100MB
  int _currentCacheSize = 0;
  
  void cacheImage(String key, Uint8List imageData) {
    if (_currentCacheSize + imageData.length > _maxCacheSize) {
      _evictOldestEntries(imageData.length);
    }
    
    _memoryCache[key] = imageData;
    _currentCacheSize += imageData.length;
  }
  
  Uint8List? getCachedImage(String key) {
    return _memoryCache[key];
  }
  
  void _evictOldestEntries(int requiredSpace) {
    final entries = _memoryCache.entries.toList();
    
    for (final entry in entries) {
      _memoryCache.remove(entry.key);
      _currentCacheSize -= entry.value.length;
      
      if (_currentCacheSize + requiredSpace <= _maxCacheSize) {
        break;
      }
    }
  }
  
  void clearCache() {
    _memoryCache.clear();
    _currentCacheSize = 0;
  }
}
```

### 异步图片加载

```dart
class AsyncImageLoader {
  static Future<Uint8List> loadImageOptimized(
    String path, {
    int? targetWidth,
    int? targetHeight,
  }) async {
    final completer = Completer<Uint8List>();
    
    // 在后台线程中加载和处理图片
    compute(_loadAndResizeImage, {
      'path': path,
      'targetWidth': targetWidth,
      'targetHeight': targetHeight,
    }).then((result) {
      completer.complete(result);
    }).catchError((error) {
      completer.completeError(error);
    });
    
    return completer.future;
  }
  
  static Future<Uint8List> _loadAndResizeImage(Map<String, dynamic> params) async {
    final path = params['path'] as String;
    final targetWidth = params['targetWidth'] as int?;
    final targetHeight = params['targetHeight'] as int?;
    
    // 加载原始图片
    final file = File(path);
    final bytes = await file.readAsBytes();
    
    // 如果需要调整大小
    if (targetWidth != null || targetHeight != null) {
      final image = img.decodeImage(bytes);
      if (image != null) {
        final resized = img.copyResize(
          image,
          width: targetWidth,
          height: targetHeight,
          interpolation: img.Interpolation.lanczos,
        );
        return Uint8List.fromList(img.encodePng(resized));
      }
    }
    
    return bytes;
  }
}
```
