# Rust 开发指南

## 项目结构

### Cargo.toml 配置

```toml
[package]
name = "tushen_core"
version = "0.1.0"
edition = "2021"
authors = ["TuShen Team"]
description = "High-performance image processing core for TuShen app"
license = "MIT"

[lib]
name = "tushen_core"
crate-type = ["cdylib", "staticlib"]

[dependencies]
# 图像处理核心库
image = { version = "0.24", features = ["jpeg", "png", "gif", "webp", "tiff"] }
imageproc = "0.23"
photon-rs = "0.3"

# 并行处理
rayon = "1.7"
tokio = { version = "1.0", features = ["full"] }

# FFI 桥接
flutter_rust_bridge = "1.82"
uniffi = "0.25"

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 数学计算
nalgebra = "0.32"
ndarray = "0.15"

# 颜色处理
palette = "0.7"

# 性能监控
criterion = { version = "0.5", features = ["html_reports"] }

[target.'cfg(target_os = "android")'.dependencies]
jni = "0.21"
ndk = "0.7"

[target.'cfg(any(target_os = "ios", target_os = "macos"))'.dependencies]
objc = "0.2"
core-graphics = "0.23"

[target.'cfg(target_os = "windows")'.dependencies]
winapi = { version = "0.3", features = ["d3d11", "dxgi"] }

[dev-dependencies]
criterion = "0.5"
proptest = "1.0"

[[bench]]
name = "image_processing"
harness = false

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 1
```

## 核心模块设计

### 图像处理器 (ImageProcessor)

```rust
use image::{ImageBuffer, Rgb, Rgba, DynamicImage};
use rayon::prelude::*;
use std::sync::Arc;

pub struct ImageProcessor {
    image: DynamicImage,
    history: Vec<ProcessingStep>,
    cache: Arc<ProcessingCache>,
}

impl ImageProcessor {
    pub fn new(image_data: Vec<u8>) -> Result<Self, ProcessingError> {
        let image = image::load_from_memory(&image_data)?;
        Ok(Self {
            image,
            history: Vec::new(),
            cache: Arc::new(ProcessingCache::new()),
        })
    }
    
    pub fn apply_filter(&mut self, filter: FilterType) -> Result<(), ProcessingError> {
        let processed = match filter {
            FilterType::Portrait(params) => self.apply_portrait_filter(params)?,
            FilterType::Landscape(params) => self.apply_landscape_filter(params)?,
            FilterType::Architecture(params) => self.apply_architecture_filter(params)?,
            FilterType::Food(params) => self.apply_food_filter(params)?,
        };
        
        self.image = processed;
        self.history.push(ProcessingStep::Filter(filter));
        Ok(())
    }
    
    pub fn resize(&mut self, width: u32, height: u32) -> Result<(), ProcessingError> {
        self.image = self.image.resize_exact(
            width, 
            height, 
            image::imageops::FilterType::Lanczos3
        );
        self.history.push(ProcessingStep::Resize { width, height });
        Ok(())
    }
    
    pub fn crop(&mut self, x: u32, y: u32, width: u32, height: u32) -> Result<(), ProcessingError> {
        self.image = self.image.crop_imm(x, y, width, height);
        self.history.push(ProcessingStep::Crop { x, y, width, height });
        Ok(())
    }
    
    pub fn get_result(&self) -> Vec<u8> {
        let mut buffer = Vec::new();
        self.image.write_to(&mut std::io::Cursor::new(&mut buffer), image::ImageOutputFormat::Png)
            .expect("Failed to encode image");
        buffer
    }
}
```

### 滤镜引擎 (FilterEngine)

```rust
pub trait Filter: Send + Sync {
    fn apply(&self, pixel: &Rgb<u8>, x: u32, y: u32) -> Rgb<u8>;
    fn name(&self) -> &str;
    fn intensity(&self) -> f32;
}

pub struct PortraitFilter {
    intensity: f32,
    skin_smoothing: f32,
    eye_enhancement: f32,
}

impl Filter for PortraitFilter {
    fn apply(&self, pixel: &Rgb<u8>, x: u32, y: u32) -> Rgb<u8> {
        // 实现人像滤镜算法
        let [r, g, b] = pixel.0;
        
        // 肤色检测
        if self.is_skin_tone(r, g, b) {
            self.apply_skin_smoothing(pixel)
        } else {
            *pixel
        }
    }
    
    fn name(&self) -> &str {
        "Portrait"
    }
    
    fn intensity(&self) -> f32 {
        self.intensity
    }
}

impl PortraitFilter {
    fn is_skin_tone(&self, r: u8, g: u8, b: u8) -> bool {
        // 肤色检测算法
        let r = r as f32 / 255.0;
        let g = g as f32 / 255.0;
        let b = b as f32 / 255.0;
        
        // YCbCr 色彩空间中的肤色范围检测
        let y = 0.299 * r + 0.587 * g + 0.114 * b;
        let cb = -0.169 * r - 0.331 * g + 0.5 * b + 0.5;
        let cr = 0.5 * r - 0.419 * g - 0.081 * b + 0.5;
        
        // 肤色范围判断
        cb >= 0.35 && cb <= 0.65 && cr >= 0.35 && cr <= 0.65
    }
    
    fn apply_skin_smoothing(&self, pixel: &Rgb<u8>) -> Rgb<u8> {
        // 磨皮算法实现
        let [r, g, b] = pixel.0;
        let smoothing = self.skin_smoothing;
        
        // 高斯模糊 + 保边滤波
        let smoothed_r = (r as f32 * (1.0 - smoothing) + 
                         self.gaussian_blur(r) * smoothing) as u8;
        let smoothed_g = (g as f32 * (1.0 - smoothing) + 
                         self.gaussian_blur(g) * smoothing) as u8;
        let smoothed_b = (b as f32 * (1.0 - smoothing) + 
                         self.gaussian_blur(b) * smoothing) as u8;
        
        Rgb([smoothed_r, smoothed_g, smoothed_b])
    }
    
    fn gaussian_blur(&self, value: u8) -> f32 {
        // 简化的高斯模糊实现
        value as f32 * 0.9
    }
}
```

### 拼图引擎 (CollageEngine)

```rust
pub struct CollageEngine {
    layout_generator: LayoutGenerator,
    image_optimizer: ImageOptimizer,
}

impl CollageEngine {
    pub fn new() -> Self {
        Self {
            layout_generator: LayoutGenerator::new(),
            image_optimizer: ImageOptimizer::new(),
        }
    }
    
    pub fn create_collage(
        &self,
        images: Vec<DynamicImage>,
        layout_type: LayoutType,
        canvas_size: (u32, u32),
    ) -> Result<DynamicImage, CollageError> {
        // 生成布局
        let layout = self.layout_generator.generate_layout(
            images.len(),
            layout_type,
            canvas_size,
        )?;
        
        // 优化图片
        let optimized_images = images
            .into_par_iter()
            .zip(layout.regions.par_iter())
            .map(|(image, region)| {
                self.image_optimizer.optimize_for_region(image, region)
            })
            .collect::<Result<Vec<_>, _>>()?;
        
        // 合成拼图
        self.compose_collage(optimized_images, layout, canvas_size)
    }
    
    fn compose_collage(
        &self,
        images: Vec<DynamicImage>,
        layout: Layout,
        canvas_size: (u32, u32),
    ) -> Result<DynamicImage, CollageError> {
        let mut canvas = ImageBuffer::new(canvas_size.0, canvas_size.1);
        
        // 填充背景
        for pixel in canvas.pixels_mut() {
            *pixel = Rgba([255, 255, 255, 255]); // 白色背景
        }
        
        // 放置图片
        for (image, region) in images.iter().zip(layout.regions.iter()) {
            let resized = image.resize_exact(
                region.width,
                region.height,
                image::imageops::FilterType::Lanczos3,
            );
            
            imageops::overlay(&mut canvas, &resized.to_rgba8(), region.x, region.y);
        }
        
        Ok(DynamicImage::ImageRgba8(canvas))
    }
}

#[derive(Debug, Clone)]
pub struct Layout {
    pub regions: Vec<Region>,
    pub style: LayoutStyle,
}

#[derive(Debug, Clone)]
pub struct Region {
    pub x: u32,
    pub y: u32,
    pub width: u32,
    pub height: u32,
    pub rotation: f32,
    pub border_radius: f32,
}

pub enum LayoutType {
    Grid { rows: u32, cols: u32 },
    Magazine,
    Story,
    Artistic,
    Minimal,
}
```

### 格式转换器 (FormatConverter)

```rust
pub struct FormatConverter;

impl FormatConverter {
    pub fn convert(
        input_data: Vec<u8>,
        input_format: ImageFormat,
        output_format: ImageFormat,
        quality: u8,
    ) -> Result<Vec<u8>, ConversionError> {
        let image = match input_format {
            ImageFormat::Jpeg => image::load_from_memory_with_format(&input_data, image::ImageFormat::Jpeg)?,
            ImageFormat::Png => image::load_from_memory_with_format(&input_data, image::ImageFormat::Png)?,
            ImageFormat::WebP => image::load_from_memory_with_format(&input_data, image::ImageFormat::WebP)?,
            ImageFormat::Heic => Self::decode_heic(&input_data)?,
            ImageFormat::Raw => Self::decode_raw(&input_data)?,
        };
        
        let mut output = Vec::new();
        let mut cursor = std::io::Cursor::new(&mut output);
        
        match output_format {
            ImageFormat::Jpeg => {
                let encoder = image::codecs::jpeg::JpegEncoder::new_with_quality(&mut cursor, quality);
                image.write_with_encoder(encoder)?;
            },
            ImageFormat::Png => {
                image.write_to(&mut cursor, image::ImageOutputFormat::Png)?;
            },
            ImageFormat::WebP => {
                Self::encode_webp(&image, &mut cursor, quality)?;
            },
            ImageFormat::Avif => {
                Self::encode_avif(&image, &mut cursor, quality)?;
            },
            _ => return Err(ConversionError::UnsupportedFormat),
        }
        
        Ok(output)
    }
    
    fn decode_heic(data: &[u8]) -> Result<DynamicImage, ConversionError> {
        // HEIC 解码实现
        // 这里需要集成 libheif 或类似库
        todo!("HEIC decoding implementation")
    }
    
    fn decode_raw(data: &[u8]) -> Result<DynamicImage, ConversionError> {
        // RAW 格式解码实现
        // 这里需要集成 rawloader 或类似库
        todo!("RAW decoding implementation")
    }
    
    fn encode_webp(
        image: &DynamicImage,
        writer: &mut dyn std::io::Write,
        quality: u8,
    ) -> Result<(), ConversionError> {
        // WebP 编码实现
        todo!("WebP encoding implementation")
    }
    
    fn encode_avif(
        image: &DynamicImage,
        writer: &mut dyn std::io::Write,
        quality: u8,
    ) -> Result<(), ConversionError> {
        // AVIF 编码实现
        todo!("AVIF encoding implementation")
    }
}
```

## 性能优化

### 并行处理

```rust
use rayon::prelude::*;

pub fn process_images_parallel<F>(
    images: Vec<DynamicImage>,
    processor: F,
) -> Vec<DynamicImage>
where
    F: Fn(DynamicImage) -> DynamicImage + Send + Sync,
{
    images
        .into_par_iter()
        .map(processor)
        .collect()
}

pub fn apply_filter_parallel(
    image: &ImageBuffer<Rgb<u8>, Vec<u8>>,
    filter: &dyn Filter,
) -> ImageBuffer<Rgb<u8>, Vec<u8>> {
    let (width, height) = image.dimensions();
    let mut output = ImageBuffer::new(width, height);
    
    output
        .enumerate_pixels_mut()
        .par_bridge()
        .for_each(|(x, y, pixel)| {
            let input_pixel = image.get_pixel(x, y);
            *pixel = filter.apply(input_pixel, x, y);
        });
    
    output
}
```

### 内存优化

```rust
pub struct MemoryPool<T> {
    pool: std::sync::Mutex<Vec<T>>,
    factory: Box<dyn Fn() -> T + Send + Sync>,
}

impl<T> MemoryPool<T> {
    pub fn new<F>(factory: F) -> Self
    where
        F: Fn() -> T + Send + Sync + 'static,
    {
        Self {
            pool: std::sync::Mutex::new(Vec::new()),
            factory: Box::new(factory),
        }
    }
    
    pub fn get(&self) -> PooledItem<T> {
        let item = {
            let mut pool = self.pool.lock().unwrap();
            pool.pop().unwrap_or_else(|| (self.factory)())
        };
        
        PooledItem {
            item: Some(item),
            pool: &self.pool,
        }
    }
}

pub struct PooledItem<'a, T> {
    item: Option<T>,
    pool: &'a std::sync::Mutex<Vec<T>>,
}

impl<T> std::ops::Deref for PooledItem<'_, T> {
    type Target = T;
    
    fn deref(&self) -> &Self::Target {
        self.item.as_ref().unwrap()
    }
}

impl<T> Drop for PooledItem<'_, T> {
    fn drop(&mut self) {
        if let Some(item) = self.item.take() {
            let mut pool = self.pool.lock().unwrap();
            pool.push(item);
        }
    }
}
```
