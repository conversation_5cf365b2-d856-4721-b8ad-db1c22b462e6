# 国际化与本地化指南

## 支持语言

- **简体中文** (zh-CN) - 中国大陆
- **繁体中文** (zh-TW) - 台湾、香港、澳门
- **English** (en) - 全球通用
- **日本語** (ja) - 日本
- **한국어** (ko) - 韩国

## 配置设置

### pubspec.yaml 配置

```yaml
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.0

flutter:
  generate: true
  assets:
    - assets/images/
    - assets/fonts/
  fonts:
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
        - asset: assets/fonts/NotoSans-Bold.ttf
          weight: 700
    - family: NotoSansCJK
      fonts:
        - asset: assets/fonts/NotoSansCJK-Regular.ttf
        - asset: assets/fonts/NotoSansCJK-Bold.ttf
          weight: 700
```

### l10n.yaml 配置

```yaml
arb-dir: lib/l10n
template-arb-file: app_en.arb
output-localization-file: app_localizations.dart
output-class: AppLocalizations
output-dir: lib/l10n/generated
nullable-getter: false
synthetic-package: false
```

## ARB 文件结构

### app_en.arb (英文模板)

```json
{
  "@@locale": "en",
  "appTitle": "TuShen",
  "@appTitle": {
    "description": "The title of the application"
  },
  
  "homeTitle": "Home",
  "@homeTitle": {
    "description": "Title for the home screen"
  },
  
  "editorTitle": "Editor",
  "@editorTitle": {
    "description": "Title for the image editor screen"
  },
  
  "collageTitle": "Collage",
  "@collageTitle": {
    "description": "Title for the collage screen"
  },
  
  "filtersTitle": "Filters",
  "@filtersTitle": {
    "description": "Title for the filters screen"
  },
  
  "converterTitle": "Converter",
  "@converterTitle": {
    "description": "Title for the format converter screen"
  },
  
  "settingsTitle": "Settings",
  "@settingsTitle": {
    "description": "Title for the settings screen"
  },
  
  "selectImages": "Select Images",
  "@selectImages": {
    "description": "Button text to select images"
  },
  
  "selectImagesCount": "Select {count} images",
  "@selectImagesCount": {
    "description": "Button text to select specific number of images",
    "placeholders": {
      "count": {
        "type": "int",
        "example": "5"
      }
    }
  },
  
  "applyFilter": "Apply Filter",
  "@applyFilter": {
    "description": "Button text to apply a filter"
  },
  
  "saveImage": "Save Image",
  "@saveImage": {
    "description": "Button text to save an image"
  },
  
  "shareImage": "Share Image",
  "@shareImage": {
    "description": "Button text to share an image"
  },
  
  "undo": "Undo",
  "@undo": {
    "description": "Button text for undo action"
  },
  
  "redo": "Redo",
  "@redo": {
    "description": "Button text for redo action"
  },
  
  "reset": "Reset",
  "@reset": {
    "description": "Button text to reset changes"
  },
  
  "cancel": "Cancel",
  "@cancel": {
    "description": "Button text to cancel an action"
  },
  
  "confirm": "Confirm",
  "@confirm": {
    "description": "Button text to confirm an action"
  },
  
  "delete": "Delete",
  "@delete": {
    "description": "Button text to delete something"
  },
  
  "filterCategoryPortrait": "Portrait",
  "@filterCategoryPortrait": {
    "description": "Filter category for portrait photos"
  },
  
  "filterCategoryLandscape": "Landscape",
  "@filterCategoryLandscape": {
    "description": "Filter category for landscape photos"
  },
  
  "filterCategoryArchitecture": "Architecture",
  "@filterCategoryArchitecture": {
    "description": "Filter category for architecture photos"
  },
  
  "filterCategoryFood": "Food",
  "@filterCategoryFood": {
    "description": "Filter category for food photos"
  },
  
  "filterCategoryArtistic": "Artistic",
  "@filterCategoryArtistic": {
    "description": "Filter category for artistic effects"
  },
  
  "collageLayoutGrid": "Grid",
  "@collageLayoutGrid": {
    "description": "Grid layout for collage"
  },
  
  "collageLayoutMagazine": "Magazine",
  "@collageLayoutMagazine": {
    "description": "Magazine style layout for collage"
  },
  
  "collageLayoutStory": "Story",
  "@collageLayoutStory": {
    "description": "Story style layout for collage"
  },
  
  "collageLayoutArtistic": "Artistic",
  "@collageLayoutArtistic": {
    "description": "Artistic layout for collage"
  },
  
  "collageLayoutMinimal": "Minimal",
  "@collageLayoutMinimal": {
    "description": "Minimal layout for collage"
  },
  
  "formatJpeg": "JPEG",
  "@formatJpeg": {
    "description": "JPEG image format"
  },
  
  "formatPng": "PNG",
  "@formatPng": {
    "description": "PNG image format"
  },
  
  "formatWebp": "WebP",
  "@formatWebp": {
    "description": "WebP image format"
  },
  
  "formatHeic": "HEIC",
  "@formatHeic": {
    "description": "HEIC image format"
  },
  
  "qualityHigh": "High Quality",
  "@qualityHigh": {
    "description": "High quality setting"
  },
  
  "qualityMedium": "Medium Quality",
  "@qualityMedium": {
    "description": "Medium quality setting"
  },
  
  "qualityLow": "Low Quality",
  "@qualityLow": {
    "description": "Low quality setting"
  },
  
  "processingImage": "Processing image...",
  "@processingImage": {
    "description": "Message shown while processing an image"
  },
  
  "savingImage": "Saving image...",
  "@savingImage": {
    "description": "Message shown while saving an image"
  },
  
  "errorLoadingImage": "Error loading image",
  "@errorLoadingImage": {
    "description": "Error message when image fails to load"
  },
  
  "errorProcessingImage": "Error processing image",
  "@errorProcessingImage": {
    "description": "Error message when image processing fails"
  },
  
  "errorSavingImage": "Error saving image",
  "@errorSavingImage": {
    "description": "Error message when image saving fails"
  },
  
  "successImageSaved": "Image saved successfully",
  "@successImageSaved": {
    "description": "Success message when image is saved"
  },
  
  "permissionCameraTitle": "Camera Permission",
  "@permissionCameraTitle": {
    "description": "Title for camera permission dialog"
  },
  
  "permissionCameraMessage": "This app needs camera access to take photos",
  "@permissionCameraMessage": {
    "description": "Message for camera permission dialog"
  },
  
  "permissionStorageTitle": "Storage Permission",
  "@permissionStorageTitle": {
    "description": "Title for storage permission dialog"
  },
  
  "permissionStorageMessage": "This app needs storage access to save images",
  "@permissionStorageMessage": {
    "description": "Message for storage permission dialog"
  },
  
  "grantPermission": "Grant Permission",
  "@grantPermission": {
    "description": "Button text to grant permission"
  },
  
  "openSettings": "Open Settings",
  "@openSettings": {
    "description": "Button text to open app settings"
  }
}
```

### app_zh.arb (简体中文)

```json
{
  "@@locale": "zh",
  "appTitle": "图神",
  "homeTitle": "首页",
  "editorTitle": "编辑器",
  "collageTitle": "拼图",
  "filtersTitle": "滤镜",
  "converterTitle": "格式转换",
  "settingsTitle": "设置",
  "selectImages": "选择图片",
  "selectImagesCount": "选择 {count} 张图片",
  "applyFilter": "应用滤镜",
  "saveImage": "保存图片",
  "shareImage": "分享图片",
  "undo": "撤销",
  "redo": "重做",
  "reset": "重置",
  "cancel": "取消",
  "confirm": "确认",
  "delete": "删除",
  "filterCategoryPortrait": "人像",
  "filterCategoryLandscape": "风景",
  "filterCategoryArchitecture": "建筑",
  "filterCategoryFood": "美食",
  "filterCategoryArtistic": "艺术",
  "collageLayoutGrid": "网格",
  "collageLayoutMagazine": "杂志",
  "collageLayoutStory": "故事",
  "collageLayoutArtistic": "艺术",
  "collageLayoutMinimal": "极简",
  "formatJpeg": "JPEG",
  "formatPng": "PNG",
  "formatWebp": "WebP",
  "formatHeic": "HEIC",
  "qualityHigh": "高质量",
  "qualityMedium": "中等质量",
  "qualityLow": "低质量",
  "processingImage": "正在处理图片...",
  "savingImage": "正在保存图片...",
  "errorLoadingImage": "图片加载失败",
  "errorProcessingImage": "图片处理失败",
  "errorSavingImage": "图片保存失败",
  "successImageSaved": "图片保存成功",
  "permissionCameraTitle": "相机权限",
  "permissionCameraMessage": "此应用需要相机权限来拍摄照片",
  "permissionStorageTitle": "存储权限",
  "permissionStorageMessage": "此应用需要存储权限来保存图片",
  "grantPermission": "授予权限",
  "openSettings": "打开设置"
}
```

### app_zh_TW.arb (繁体中文)

```json
{
  "@@locale": "zh_TW",
  "appTitle": "圖神",
  "homeTitle": "首頁",
  "editorTitle": "編輯器",
  "collageTitle": "拼圖",
  "filtersTitle": "濾鏡",
  "converterTitle": "格式轉換",
  "settingsTitle": "設定",
  "selectImages": "選擇圖片",
  "selectImagesCount": "選擇 {count} 張圖片",
  "applyFilter": "套用濾鏡",
  "saveImage": "儲存圖片",
  "shareImage": "分享圖片",
  "undo": "復原",
  "redo": "重做",
  "reset": "重設",
  "cancel": "取消",
  "confirm": "確認",
  "delete": "刪除",
  "filterCategoryPortrait": "人像",
  "filterCategoryLandscape": "風景",
  "filterCategoryArchitecture": "建築",
  "filterCategoryFood": "美食",
  "filterCategoryArtistic": "藝術",
  "collageLayoutGrid": "網格",
  "collageLayoutMagazine": "雜誌",
  "collageLayoutStory": "故事",
  "collageLayoutArtistic": "藝術",
  "collageLayoutMinimal": "極簡",
  "formatJpeg": "JPEG",
  "formatPng": "PNG",
  "formatWebp": "WebP",
  "formatHeic": "HEIC",
  "qualityHigh": "高品質",
  "qualityMedium": "中等品質",
  "qualityLow": "低品質",
  "processingImage": "正在處理圖片...",
  "savingImage": "正在儲存圖片...",
  "errorLoadingImage": "圖片載入失敗",
  "errorProcessingImage": "圖片處理失敗",
  "errorSavingImage": "圖片儲存失敗",
  "successImageSaved": "圖片儲存成功",
  "permissionCameraTitle": "相機權限",
  "permissionCameraMessage": "此應用程式需要相機權限來拍攝照片",
  "permissionStorageTitle": "儲存權限",
  "permissionStorageMessage": "此應用程式需要儲存權限來儲存圖片",
  "grantPermission": "授予權限",
  "openSettings": "開啟設定"
}
```

## 应用配置

### main.dart 国际化设置

```dart
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'l10n/generated/app_localizations.dart';

void main() {
  runApp(const TuShenApp());
}

class TuShenApp extends StatelessWidget {
  const TuShenApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'TuShen',
      
      // 国际化配置
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      
      supportedLocales: const [
        Locale('en'),           // English
        Locale('zh'),           // 简体中文
        Locale('zh', 'TW'),     // 繁体中文
        Locale('ja'),           // 日本語
        Locale('ko'),           // 한국어
      ],
      
      // 语言回退策略
      localeResolutionCallback: (locale, supportedLocales) {
        if (locale != null) {
          // 精确匹配
          for (final supportedLocale in supportedLocales) {
            if (supportedLocale.languageCode == locale.languageCode &&
                supportedLocale.countryCode == locale.countryCode) {
              return supportedLocale;
            }
          }
          
          // 语言匹配
          for (final supportedLocale in supportedLocales) {
            if (supportedLocale.languageCode == locale.languageCode) {
              return supportedLocale;
            }
          }
        }
        
        // 默认返回英文
        return const Locale('en');
      },
      
      home: const HomeScreen(),
    );
  }
}
```

### 本地化工具类

```dart
import 'package:flutter/material.dart';
import 'l10n/generated/app_localizations.dart';

class L10n {
  static AppLocalizations of(BuildContext context) {
    return AppLocalizations.of(context)!;
  }
  
  static bool isRTL(BuildContext context) {
    return Directionality.of(context) == TextDirection.rtl;
  }
  
  static bool isCJK(BuildContext context) {
    final locale = Localizations.localeOf(context);
    return ['zh', 'ja', 'ko'].contains(locale.languageCode);
  }
  
  static String getLocaleName(BuildContext context) {
    final locale = Localizations.localeOf(context);
    switch (locale.toString()) {
      case 'zh':
        return '简体中文';
      case 'zh_TW':
        return '繁體中文';
      case 'ja':
        return '日本語';
      case 'ko':
        return '한국어';
      case 'en':
      default:
        return 'English';
    }
  }
}
```

## 字体配置

### 多语言字体支持

```dart
class AppTextTheme {
  static TextTheme getTextTheme(Locale locale) {
    final isCJK = ['zh', 'ja', 'ko'].contains(locale.languageCode);
    final fontFamily = isCJK ? 'NotoSansCJK' : 'NotoSans';
    
    return TextTheme(
      displayLarge: TextStyle(
        fontFamily: fontFamily,
        fontSize: 32,
        fontWeight: FontWeight.w700,
        letterSpacing: isCJK ? 0 : -0.5,
      ),
      displayMedium: TextStyle(
        fontFamily: fontFamily,
        fontSize: 24,
        fontWeight: FontWeight.w600,
        letterSpacing: isCJK ? 0 : -0.25,
      ),
      bodyLarge: TextStyle(
        fontFamily: fontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: isCJK ? 0 : 0.15,
      ),
      bodyMedium: TextStyle(
        fontFamily: fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: isCJK ? 0 : 0.25,
      ),
    );
  }
}
```

## 最佳实践

### 1. 文本长度适配

```dart
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final int? maxLines;
  
  const ResponsiveText(
    this.text, {
    Key? key,
    this.style,
    this.maxLines,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final isCJK = ['zh', 'ja', 'ko'].contains(locale.languageCode);
    
    return Text(
      text,
      style: style,
      maxLines: maxLines,
      overflow: TextOverflow.ellipsis,
      textScaleFactor: isCJK ? 0.9 : 1.0, // CJK 文字稍微缩小
    );
  }
}
```

### 2. 日期时间本地化

```dart
import 'package:intl/intl.dart';

class DateTimeFormatter {
  static String formatDate(DateTime date, BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    return DateFormat.yMMMd(locale).format(date);
  }
  
  static String formatTime(DateTime time, BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    return DateFormat.Hm(locale).format(time);
  }
  
  static String formatDateTime(DateTime dateTime, BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    return DateFormat.yMMMd(locale).add_Hm().format(dateTime);
  }
}
```

### 3. 数字格式化

```dart
import 'package:intl/intl.dart';

class NumberFormatter {
  static String formatNumber(num number, BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    return NumberFormat.decimalPattern(locale).format(number);
  }
  
  static String formatCurrency(double amount, BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    return NumberFormat.currency(locale: locale).format(amount);
  }
  
  static String formatPercentage(double percentage, BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    return NumberFormat.percentPattern(locale).format(percentage);
  }
}
```
