//! Core image processing functionality

pub mod image_processor;
pub mod collage_engine;
pub mod format_converter;
pub mod live_photo;

// Re-export main types
pub use image_processor::ImageProcessor;
pub use collage_engine::{CollageEngine, CollageLayout, LayoutType, Region};
pub use format_converter::FormatConverter;
pub use live_photo::LivePhotoEngine;

use crate::utils::{ImageData, ProcessingOptions};
use crate::error::TuShenResult;

/// Main interface for TuShen image processing operations
pub struct TuShenCore {
    processor: ImageProcessor,
    collage_engine: CollageEngine,
    converter: FormatConverter,
    live_photo_engine: LivePhotoEngine,
}

impl TuShenCore {
    /// Create a new TuShen core instance
    pub fn new() -> Self {
        Self {
            processor: ImageProcessor::new(),
            collage_engine: CollageEngine::new(),
            converter: FormatConverter::new(),
            live_photo_engine: LivePhotoEngine::new(),
        }
    }
    
    /// Process a single image with the given options
    pub async fn process_image(
        &mut self,
        image_data: ImageData,
        options: ProcessingOptions,
    ) -> TuShenResult<ImageData> {
        self.processor.process(image_data, options).await
    }
    
    /// Create a collage from multiple images
    pub async fn create_collage(
        &mut self,
        images: Vec<ImageData>,
        layout: CollageLayout,
        options: ProcessingOptions,
    ) -> TuShenResult<ImageData> {
        self.collage_engine.create_collage(images, layout, options).await
    }
    
    /// Convert image format
    pub async fn convert_format(
        &mut self,
        image_data: ImageData,
        target_format: crate::utils::ImageFormat,
        options: ProcessingOptions,
    ) -> TuShenResult<ImageData> {
        self.converter.convert(image_data, target_format, options).await
    }
    
    /// Create a live photo from image and video data
    pub async fn create_live_photo(
        &mut self,
        image_data: ImageData,
        video_data: Vec<u8>,
        options: ProcessingOptions,
    ) -> TuShenResult<Vec<u8>> {
        self.live_photo_engine.create_live_photo(image_data, video_data, options).await
    }
    
    /// Get processing capabilities for the current platform
    pub fn get_capabilities() -> ProcessingCapabilities {
        ProcessingCapabilities {
            max_image_size: get_max_image_size(),
            supported_formats: get_supported_formats(),
            gpu_acceleration: is_gpu_available(),
            parallel_processing: true,
            max_collage_images: 20,
            live_photo_support: is_live_photo_supported(),
        }
    }
}

impl Default for TuShenCore {
    fn default() -> Self {
        Self::new()
    }
}

/// Processing capabilities of the current platform
#[derive(Debug, Clone)]
pub struct ProcessingCapabilities {
    /// Maximum supported image size (width * height)
    pub max_image_size: u64,
    /// List of supported image formats
    pub supported_formats: Vec<crate::utils::ImageFormat>,
    /// Whether GPU acceleration is available
    pub gpu_acceleration: bool,
    /// Whether parallel processing is supported
    pub parallel_processing: bool,
    /// Maximum number of images in a collage
    pub max_collage_images: usize,
    /// Whether live photo creation is supported
    pub live_photo_support: bool,
}

fn get_max_image_size() -> u64 {
    // Platform-specific maximum image size
    #[cfg(target_os = "android")]
    {
        // Android typically has lower memory limits
        50_000_000 // 50 megapixels
    }
    
    #[cfg(target_os = "ios")]
    {
        // iOS has good memory management
        100_000_000 // 100 megapixels
    }
    
    #[cfg(any(target_os = "macos", target_os = "windows", target_os = "linux"))]
    {
        // Desktop platforms can handle larger images
        200_000_000 // 200 megapixels
    }
    
    #[cfg(not(any(target_os = "android", target_os = "ios", target_os = "macos", target_os = "windows", target_os = "linux")))]
    {
        // Conservative default for unknown platforms
        25_000_000 // 25 megapixels
    }
}

fn get_supported_formats() -> Vec<crate::utils::ImageFormat> {
    use crate::utils::ImageFormat;
    
    let mut formats = vec![
        ImageFormat::Jpeg,
        ImageFormat::Png,
        ImageFormat::WebP,
        ImageFormat::Bmp,
        ImageFormat::Gif,
        ImageFormat::Tiff,
    ];
    
    #[cfg(feature = "heic")]
    formats.push(ImageFormat::Heic);
    
    #[cfg(feature = "avif")]
    formats.push(ImageFormat::Avif);
    
    #[cfg(feature = "raw")]
    {
        use crate::utils::RawFormat;
        formats.extend([
            ImageFormat::Raw(RawFormat::Cr2),
            ImageFormat::Raw(RawFormat::Nef),
            ImageFormat::Raw(RawFormat::Arw),
            ImageFormat::Raw(RawFormat::Dng),
        ]);
    }
    
    formats
}

fn is_gpu_available() -> bool {
    #[cfg(feature = "gpu-acceleration")]
    {
        // Platform-specific GPU detection
        #[cfg(target_os = "android")]
        {
            // Check for Vulkan support on Android
            // This would require actual Vulkan initialization
            true // Assume available for now
        }
        
        #[cfg(any(target_os = "ios", target_os = "macos"))]
        {
            // Metal is available on all modern iOS/macOS devices
            true
        }
        
        #[cfg(target_os = "windows")]
        {
            // Check for DirectX support on Windows
            // This would require actual DirectX initialization
            true // Assume available for now
        }
        
        #[cfg(target_os = "linux")]
        {
            // Check for OpenGL/Vulkan support on Linux
            // This would require actual GPU detection
            false // Conservative default
        }
        
        #[cfg(not(any(target_os = "android", target_os = "ios", target_os = "macos", target_os = "windows", target_os = "linux")))]
        {
            false
        }
    }
    
    #[cfg(not(feature = "gpu-acceleration"))]
    {
        false
    }
}

fn is_live_photo_supported() -> bool {
    #[cfg(feature = "live-photo")]
    {
        #[cfg(any(target_os = "ios", target_os = "macos"))]
        {
            true // Native Live Photo support
        }
        
        #[cfg(target_os = "android")]
        {
            true // Motion Photo support
        }
        
        #[cfg(not(any(target_os = "ios", target_os = "macos", target_os = "android")))]
        {
            false
        }
    }
    
    #[cfg(not(feature = "live-photo"))]
    {
        false
    }
}

/// Utility function to validate image data
pub fn validate_image_data(image_data: &ImageData) -> TuShenResult<()> {
    use crate::error::TuShenError;
    
    // Check dimensions
    if image_data.width == 0 || image_data.height == 0 {
        return Err(TuShenError::invalid_input("Image dimensions cannot be zero"));
    }
    
    // Check maximum size
    let pixel_count = image_data.width as u64 * image_data.height as u64;
    if pixel_count > get_max_image_size() {
        return Err(TuShenError::invalid_input(format!(
            "Image too large: {} pixels (max: {})",
            pixel_count,
            get_max_image_size()
        )));
    }
    
    // Check data size
    if image_data.data.is_empty() {
        return Err(TuShenError::invalid_input("Image data cannot be empty"));
    }
    
    // Check format support
    let supported_formats = get_supported_formats();
    if !supported_formats.contains(&image_data.format) {
        return Err(TuShenError::unsupported(format!(
            "Image format {:?} is not supported on this platform",
            image_data.format
        )));
    }
    
    Ok(())
}

/// Utility function to estimate processing time
pub fn estimate_processing_time(
    image_data: &ImageData,
    operation: &str,
) -> std::time::Duration {
    use std::time::Duration;
    
    let pixel_count = image_data.width as u64 * image_data.height as u64;
    let base_time_per_megapixel = Duration::from_millis(100);
    let megapixels = pixel_count as f64 / 1_000_000.0;
    
    let multiplier = match operation {
        "filter" => 1.0,
        "collage" => 2.0,
        "conversion" => 0.5,
        "live_photo" => 3.0,
        _ => 1.0,
    };
    
    Duration::from_millis((base_time_per_megapixel.as_millis() as f64 * megapixels * multiplier) as u64)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::{ImageData, ImageFormat};

    #[test]
    fn test_tushen_core_creation() {
        let core = TuShenCore::new();
        // Just test that it can be created without panicking
        drop(core);
    }

    #[test]
    fn test_capabilities() {
        let capabilities = TuShenCore::get_capabilities();
        assert!(capabilities.max_image_size > 0);
        assert!(!capabilities.supported_formats.is_empty());
        assert!(capabilities.max_collage_images > 0);
    }

    #[test]
    fn test_validate_image_data() {
        let valid_image = ImageData::new(
            vec![0; 1000],
            100,
            100,
            ImageFormat::Jpeg,
        );
        assert!(validate_image_data(&valid_image).is_ok());
        
        let invalid_image = ImageData::new(
            vec![],
            0,
            0,
            ImageFormat::Jpeg,
        );
        assert!(validate_image_data(&invalid_image).is_err());
    }

    #[test]
    fn test_estimate_processing_time() {
        let image = ImageData::new(
            vec![0; 1000],
            1000,
            1000,
            ImageFormat::Jpeg,
        );
        
        let filter_time = estimate_processing_time(&image, "filter");
        let collage_time = estimate_processing_time(&image, "collage");
        
        assert!(collage_time > filter_time);
    }
}
