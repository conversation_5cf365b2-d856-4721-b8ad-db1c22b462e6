//! Utility types and functions for TuShen Core

// pub mod color;
// pub mod geometry;
pub mod performance;
// pub mod memory;

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Image data container with metadata
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ImageData {
    /// Raw image bytes
    pub data: Vec<u8>,
    /// Image width in pixels
    pub width: u32,
    /// Image height in pixels
    pub height: u32,
    /// Image format
    pub format: ImageFormat,
    /// Color space information
    pub color_space: ColorSpace,
    /// EXIF metadata (if available)
    pub metadata: Option<ImageMetadata>,
}

/// Supported image formats
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ImageFormat {
    /// JPEG format
    Jpeg,
    /// PNG format
    Png,
    /// WebP format
    WebP,
    /// HEIC format (iOS)
    Heic,
    /// AVIF format
    Avif,
    /// TIFF format
    Tiff,
    /// BMP format
    Bmp,
    /// GIF format
    Gif,
    /// ICO format
    Ico,
    /// RAW formats
    Raw(RawFormat),
}

/// RAW image formats
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum RawFormat {
    /// Canon CR2
    Cr2,
    /// Nikon NEF
    Nef,
    /// Sony ARW
    Arw,
    /// Adobe DNG
    Dng,
    /// Other RAW format
    Other,
}

/// Color space information
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ColorSpace {
    /// sRGB color space
    Srgb,
    /// Adobe RGB color space
    AdobeRgb,
    /// Display P3 color space
    DisplayP3,
    /// Rec. 2020 color space
    Rec2020,
    /// ProPhoto RGB color space
    ProPhotoRgb,
    /// Unknown or custom color space
    Unknown,
}

/// Image metadata container
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImageMetadata {
    /// Camera make
    pub camera_make: Option<String>,
    /// Camera model
    pub camera_model: Option<String>,
    /// Lens information
    pub lens_info: Option<String>,
    /// ISO sensitivity
    pub iso: Option<u32>,
    /// Aperture value (f-number)
    pub aperture: Option<f32>,
    /// Shutter speed (in seconds)
    pub shutter_speed: Option<f32>,
    /// Focal length (in mm)
    pub focal_length: Option<f32>,
    /// Date and time when image was taken
    pub date_time: Option<chrono::DateTime<chrono::Utc>>,
    /// GPS coordinates
    pub gps_info: Option<GpsInfo>,
    /// Image orientation
    pub orientation: Option<Orientation>,
    /// Additional EXIF data
    pub additional_data: HashMap<String, String>,
}

/// GPS information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GpsInfo {
    /// Latitude in decimal degrees
    pub latitude: f64,
    /// Longitude in decimal degrees
    pub longitude: f64,
    /// Altitude in meters (if available)
    pub altitude: Option<f64>,
}

/// Image orientation
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum Orientation {
    /// Normal orientation
    Normal,
    /// Rotated 90 degrees clockwise
    Rotate90,
    /// Rotated 180 degrees
    Rotate180,
    /// Rotated 270 degrees clockwise
    Rotate270,
    /// Flipped horizontally
    FlipHorizontal,
    /// Flipped vertically
    FlipVertical,
    /// Flipped horizontally and rotated 90 degrees
    FlipHorizontalRotate90,
    /// Flipped horizontally and rotated 270 degrees
    FlipHorizontalRotate270,
}

/// Processing options for image operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingOptions {
    /// Quality setting (0-100)
    pub quality: u8,
    /// Whether to preserve metadata
    pub preserve_metadata: bool,
    /// Whether to use GPU acceleration
    pub use_gpu: bool,
    /// Maximum processing time in seconds
    pub timeout_seconds: u64,
    /// Memory limit in bytes
    pub memory_limit: Option<usize>,
    /// Number of threads to use (None = auto)
    pub thread_count: Option<usize>,
    /// Whether to enable progressive encoding
    pub progressive: bool,
    /// Color space for output
    pub output_color_space: Option<ColorSpace>,
}

impl Default for ProcessingOptions {
    fn default() -> Self {
        Self {
            quality: 85,
            preserve_metadata: true,
            use_gpu: true,
            timeout_seconds: 30,
            memory_limit: None,
            thread_count: None,
            progressive: false,
            output_color_space: None,
        }
    }
}

impl ImageFormat {
    /// Get the file extension for this format
    pub fn extension(&self) -> &'static str {
        match self {
            ImageFormat::Jpeg => "jpg",
            ImageFormat::Png => "png",
            ImageFormat::WebP => "webp",
            ImageFormat::Heic => "heic",
            ImageFormat::Avif => "avif",
            ImageFormat::Tiff => "tiff",
            ImageFormat::Bmp => "bmp",
            ImageFormat::Gif => "gif",
            ImageFormat::Ico => "ico",
            ImageFormat::Raw(RawFormat::Cr2) => "cr2",
            ImageFormat::Raw(RawFormat::Nef) => "nef",
            ImageFormat::Raw(RawFormat::Arw) => "arw",
            ImageFormat::Raw(RawFormat::Dng) => "dng",
            ImageFormat::Raw(RawFormat::Other) => "raw",
        }
    }
    
    /// Get the MIME type for this format
    pub fn mime_type(&self) -> &'static str {
        match self {
            ImageFormat::Jpeg => "image/jpeg",
            ImageFormat::Png => "image/png",
            ImageFormat::WebP => "image/webp",
            ImageFormat::Heic => "image/heic",
            ImageFormat::Avif => "image/avif",
            ImageFormat::Tiff => "image/tiff",
            ImageFormat::Bmp => "image/bmp",
            ImageFormat::Gif => "image/gif",
            ImageFormat::Ico => "image/x-icon",
            ImageFormat::Raw(_) => "image/x-raw",
        }
    }
    
    /// Check if this format supports transparency
    pub fn supports_transparency(&self) -> bool {
        matches!(self, ImageFormat::Png | ImageFormat::WebP | ImageFormat::Gif)
    }
    
    /// Check if this format supports animation
    pub fn supports_animation(&self) -> bool {
        matches!(self, ImageFormat::Gif | ImageFormat::WebP)
    }
    
    /// Check if this format is lossless
    pub fn is_lossless(&self) -> bool {
        matches!(self, ImageFormat::Png | ImageFormat::Tiff | ImageFormat::Bmp)
    }
    
    /// Detect format from file extension
    pub fn from_extension(ext: &str) -> Option<Self> {
        match ext.to_lowercase().as_str() {
            "jpg" | "jpeg" => Some(ImageFormat::Jpeg),
            "png" => Some(ImageFormat::Png),
            "webp" => Some(ImageFormat::WebP),
            "heic" | "heif" => Some(ImageFormat::Heic),
            "avif" => Some(ImageFormat::Avif),
            "tiff" | "tif" => Some(ImageFormat::Tiff),
            "bmp" => Some(ImageFormat::Bmp),
            "gif" => Some(ImageFormat::Gif),
            "ico" => Some(ImageFormat::Ico),
            "cr2" => Some(ImageFormat::Raw(RawFormat::Cr2)),
            "nef" => Some(ImageFormat::Raw(RawFormat::Nef)),
            "arw" => Some(ImageFormat::Raw(RawFormat::Arw)),
            "dng" => Some(ImageFormat::Raw(RawFormat::Dng)),
            _ => None,
        }
    }
}

impl ImageData {
    /// Create new ImageData instance
    pub fn new(
        data: Vec<u8>,
        width: u32,
        height: u32,
        format: ImageFormat,
    ) -> Self {
        Self {
            data,
            width,
            height,
            format,
            color_space: ColorSpace::Srgb,
            metadata: None,
        }
    }
    
    /// Get the aspect ratio of the image
    pub fn aspect_ratio(&self) -> f32 {
        self.width as f32 / self.height as f32
    }
    
    /// Get the total number of pixels
    pub fn pixel_count(&self) -> u64 {
        self.width as u64 * self.height as u64
    }
    
    /// Get the estimated memory usage in bytes
    pub fn memory_usage(&self) -> usize {
        // Assume 4 bytes per pixel (RGBA)
        (self.width as usize * self.height as usize * 4) + self.data.len()
    }
    
    /// Check if the image is in portrait orientation
    pub fn is_portrait(&self) -> bool {
        self.height > self.width
    }
    
    /// Check if the image is in landscape orientation
    pub fn is_landscape(&self) -> bool {
        self.width > self.height
    }
    
    /// Check if the image is square
    pub fn is_square(&self) -> bool {
        self.width == self.height
    }
    
    /// Get a description of the image
    pub fn description(&self) -> String {
        format!(
            "{}x{} {} image ({:.1} MP, {:.1} KB)",
            self.width,
            self.height,
            self.format.extension().to_uppercase(),
            self.pixel_count() as f64 / 1_000_000.0,
            self.data.len() as f64 / 1024.0
        )
    }
}

// Re-export utility modules
// pub use color::*;
// pub use geometry::*;
pub use performance::PerformanceMonitor;
// pub use memory::*;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_image_format_extension() {
        assert_eq!(ImageFormat::Jpeg.extension(), "jpg");
        assert_eq!(ImageFormat::Png.extension(), "png");
        assert_eq!(ImageFormat::WebP.extension(), "webp");
    }

    #[test]
    fn test_image_format_from_extension() {
        assert_eq!(ImageFormat::from_extension("jpg"), Some(ImageFormat::Jpeg));
        assert_eq!(ImageFormat::from_extension("PNG"), Some(ImageFormat::Png));
        assert_eq!(ImageFormat::from_extension("unknown"), None);
    }

    #[test]
    fn test_image_format_properties() {
        assert!(ImageFormat::Png.supports_transparency());
        assert!(!ImageFormat::Jpeg.supports_transparency());
        assert!(ImageFormat::Png.is_lossless());
        assert!(!ImageFormat::Jpeg.is_lossless());
    }

    #[test]
    fn test_image_data_properties() {
        let image_data = ImageData::new(
            vec![0; 1000],
            100,
            200,
            ImageFormat::Png,
        );
        
        assert_eq!(image_data.aspect_ratio(), 0.5);
        assert_eq!(image_data.pixel_count(), 20000);
        assert!(image_data.is_portrait());
        assert!(!image_data.is_landscape());
        assert!(!image_data.is_square());
    }

    #[test]
    fn test_processing_options_default() {
        let options = ProcessingOptions::default();
        assert_eq!(options.quality, 85);
        assert!(options.preserve_metadata);
        assert!(options.use_gpu);
    }
}
