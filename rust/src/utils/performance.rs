//! Performance monitoring utilities

use std::collections::HashMap;
use std::time::{Duration, Instant};
use serde::{Deserialize, Serialize};

/// Performance monitoring system
#[derive(Debug)]
pub struct PerformanceMonitor {
    session_start: Option<Instant>,
    operations: HashMap<String, OperationStats>,
    current_operations: HashMap<String, Instant>,
    memory_tracker: MemoryTracker,
}

/// Statistics for a specific operation type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OperationStats {
    /// Total number of operations
    pub count: u64,
    /// Total time spent on operations
    pub total_duration: Duration,
    /// Minimum operation duration
    pub min_duration: Duration,
    /// Maximum operation duration
    pub max_duration: Duration,
    /// Average operation duration
    pub avg_duration: Duration,
    /// Number of failed operations
    pub failures: u64,
    /// Peak memory usage during operations
    pub peak_memory: usize,
}

/// Memory usage tracking
#[derive(Debug)]
pub struct MemoryTracker {
    peak_usage: usize,
    current_usage: usize,
    allocations: u64,
    deallocations: u64,
}

impl PerformanceMonitor {
    /// Create a new performance monitor
    pub fn new() -> Self {
        Self {
            session_start: None,
            operations: HashMap::new(),
            current_operations: HashMap::new(),
            memory_tracker: MemoryTracker::new(),
        }
    }
    
    /// Start a monitoring session
    pub fn start_session(&mut self) {
        self.session_start = Some(Instant::now());
        self.operations.clear();
        self.current_operations.clear();
        self.memory_tracker.reset();
    }
    
    /// End the monitoring session
    pub fn end_session(&mut self) {
        self.session_start = None;
        self.current_operations.clear();
    }
    
    /// Start timing an operation
    pub fn start_operation(&mut self, operation_name: &str) {
        let operation_id = format!("{}_{}", operation_name, std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_nanos());
        self.current_operations.insert(operation_id, Instant::now());
    }
    
    /// End timing an operation
    pub fn end_operation(&mut self, operation_name: &str, success: bool) {
        // Find the most recent operation with this name
        let operation_id = self.current_operations
            .keys()
            .find(|key| key.starts_with(operation_name))
            .cloned();
        
        if let Some(id) = operation_id {
            if let Some(start_time) = self.current_operations.remove(&id) {
                let duration = start_time.elapsed();
                let memory_usage = self.memory_tracker.current_usage();
                
                self.record_operation(operation_name, duration, success, memory_usage);
            }
        }
    }
    
    /// Record an operation with its duration
    fn record_operation(&mut self, operation_name: &str, duration: Duration, success: bool, memory_usage: usize) {
        let stats = self.operations.entry(operation_name.to_string()).or_insert_with(|| {
            OperationStats {
                count: 0,
                total_duration: Duration::ZERO,
                min_duration: Duration::MAX,
                max_duration: Duration::ZERO,
                avg_duration: Duration::ZERO,
                failures: 0,
                peak_memory: 0,
            }
        });
        
        stats.count += 1;
        stats.total_duration += duration;
        
        if duration < stats.min_duration {
            stats.min_duration = duration;
        }
        
        if duration > stats.max_duration {
            stats.max_duration = duration;
        }
        
        stats.avg_duration = stats.total_duration / stats.count as u32;
        
        if !success {
            stats.failures += 1;
        }
        
        if memory_usage > stats.peak_memory {
            stats.peak_memory = memory_usage;
        }
    }
    
    /// Measure the execution time of a closure
    pub fn measure<T, F>(&mut self, operation_name: &str, operation: F) -> (T, Duration)
    where
        F: FnOnce() -> T,
    {
        let start = Instant::now();
        let result = operation();
        let duration = start.elapsed();
        
        self.record_operation(operation_name, duration, true, self.memory_tracker.current_usage());
        
        (result, duration)
    }
    
    /// Measure the execution time of a fallible closure
    pub fn measure_result<T, E, F>(&mut self, operation_name: &str, operation: F) -> (Result<T, E>, Duration)
    where
        F: FnOnce() -> Result<T, E>,
    {
        let start = Instant::now();
        let result = operation();
        let duration = start.elapsed();
        let success = result.is_ok();
        
        self.record_operation(operation_name, duration, success, self.memory_tracker.current_usage());
        
        (result, duration)
    }
    
    /// Get statistics for a specific operation
    pub fn get_operation_stats(&self, operation_name: &str) -> Option<&OperationStats> {
        self.operations.get(operation_name)
    }
    
    /// Get all operation statistics
    pub fn get_all_stats(&self) -> &HashMap<String, OperationStats> {
        &self.operations
    }
    
    /// Get session duration
    pub fn session_duration(&self) -> Option<Duration> {
        self.session_start.map(|start| start.elapsed())
    }
    
    /// Get memory statistics
    pub fn memory_stats(&self) -> &MemoryTracker {
        &self.memory_tracker
    }
    
    /// Get performance summary as JSON
    pub fn get_stats_json(&self) -> String {
        let summary = PerformanceSummary {
            session_duration: self.session_duration(),
            operations: self.operations.clone(),
            memory_stats: MemoryStats {
                peak_usage: self.memory_tracker.peak_usage,
                current_usage: self.memory_tracker.current_usage,
                allocations: self.memory_tracker.allocations,
                deallocations: self.memory_tracker.deallocations,
            },
        };
        
        serde_json::to_string_pretty(&summary).unwrap_or_else(|_| "{}".to_string())
    }
    
    /// Update memory usage
    pub fn update_memory_usage(&mut self, current_usage: usize) {
        self.memory_tracker.update_usage(current_usage);
    }
    
    /// Record memory allocation
    pub fn record_allocation(&mut self, size: usize) {
        self.memory_tracker.record_allocation(size);
    }
    
    /// Record memory deallocation
    pub fn record_deallocation(&mut self, size: usize) {
        self.memory_tracker.record_deallocation(size);
    }
}

impl MemoryTracker {
    fn new() -> Self {
        Self {
            peak_usage: 0,
            current_usage: 0,
            allocations: 0,
            deallocations: 0,
        }
    }
    
    fn reset(&mut self) {
        self.peak_usage = 0;
        self.current_usage = 0;
        self.allocations = 0;
        self.deallocations = 0;
    }
    
    fn update_usage(&mut self, usage: usize) {
        self.current_usage = usage;
        if usage > self.peak_usage {
            self.peak_usage = usage;
        }
    }
    
    fn record_allocation(&mut self, size: usize) {
        self.allocations += 1;
        self.current_usage += size;
        if self.current_usage > self.peak_usage {
            self.peak_usage = self.current_usage;
        }
    }
    
    fn record_deallocation(&mut self, size: usize) {
        self.deallocations += 1;
        self.current_usage = self.current_usage.saturating_sub(size);
    }
    
    pub fn current_usage(&self) -> usize {
        self.current_usage
    }
    
    pub fn peak_usage(&self) -> usize {
        self.peak_usage
    }
    
    pub fn allocation_count(&self) -> u64 {
        self.allocations
    }
    
    pub fn deallocation_count(&self) -> u64 {
        self.deallocations
    }
}

#[derive(Debug, Serialize, Deserialize)]
struct PerformanceSummary {
    session_duration: Option<Duration>,
    operations: HashMap<String, OperationStats>,
    memory_stats: MemoryStats,
}

#[derive(Debug, Serialize, Deserialize)]
struct MemoryStats {
    peak_usage: usize,
    current_usage: usize,
    allocations: u64,
    deallocations: u64,
}

impl Default for PerformanceMonitor {
    fn default() -> Self {
        Self::new()
    }
}

/// Convenience macro for measuring operation performance
#[macro_export]
macro_rules! measure_performance {
    ($monitor:expr, $operation_name:expr, $operation:expr) => {
        $monitor.measure($operation_name, || $operation)
    };
}

/// Convenience macro for measuring fallible operation performance
#[macro_export]
macro_rules! measure_performance_result {
    ($monitor:expr, $operation_name:expr, $operation:expr) => {
        $monitor.measure_result($operation_name, || $operation)
    };
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;

    #[test]
    fn test_performance_monitor_basic() {
        let mut monitor = PerformanceMonitor::new();
        monitor.start_session();
        
        // Simulate some work
        let (result, duration) = monitor.measure("test_operation", || {
            thread::sleep(Duration::from_millis(10));
            42
        });
        
        assert_eq!(result, 42);
        assert!(duration >= Duration::from_millis(10));
        
        let stats = monitor.get_operation_stats("test_operation").unwrap();
        assert_eq!(stats.count, 1);
        assert!(stats.avg_duration >= Duration::from_millis(10));
    }

    #[test]
    fn test_memory_tracking() {
        let mut monitor = PerformanceMonitor::new();
        monitor.start_session();
        
        monitor.record_allocation(1000);
        assert_eq!(monitor.memory_stats().current_usage(), 1000);
        assert_eq!(monitor.memory_stats().peak_usage(), 1000);
        
        monitor.record_allocation(500);
        assert_eq!(monitor.memory_stats().current_usage(), 1500);
        assert_eq!(monitor.memory_stats().peak_usage(), 1500);
        
        monitor.record_deallocation(200);
        assert_eq!(monitor.memory_stats().current_usage(), 1300);
        assert_eq!(monitor.memory_stats().peak_usage(), 1500);
    }

    #[test]
    fn test_operation_failure_tracking() {
        let mut monitor = PerformanceMonitor::new();
        monitor.start_session();
        
        // Successful operation
        let (_result, _duration) = monitor.measure_result("test_op", || -> Result<i32, &str> {
            Ok(42)
        });
        
        // Failed operation
        let (_result, _duration) = monitor.measure_result("test_op", || -> Result<i32, &str> {
            Err("test error")
        });
        
        let stats = monitor.get_operation_stats("test_op").unwrap();
        assert_eq!(stats.count, 2);
        assert_eq!(stats.failures, 1);
    }

    #[test]
    fn test_stats_json_serialization() {
        let mut monitor = PerformanceMonitor::new();
        monitor.start_session();
        
        monitor.measure("test", || thread::sleep(Duration::from_millis(1)));
        
        let json = monitor.get_stats_json();
        assert!(json.contains("test"));
        assert!(json.contains("session_duration"));
        assert!(json.contains("memory_stats"));
    }
}
