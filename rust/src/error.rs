//! Error types for TuShen Core

use thiserror::Error;

/// Main error type for TuShen operations
#[derive(Error, Debug)]
pub enum TuShenError {
    #[error("Image processing error: {0}")]
    Processing(#[from] ProcessingError),
    
    #[error("Collage creation error: {0}")]
    Collage(#[from] CollageError),
    
    #[error("Format conversion error: {0}")]
    Conversion(#[from] ConversionError),
    
    #[error("Filter application error: {0}")]
    Filter(#[from] FilterError),
    
    #[error("I/O error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("Internal error: {0}")]
    Internal(String),
    
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    
    #[error("Unsupported operation: {0}")]
    UnsupportedOperation(String),
    
    #[error("Memory allocation error: {0}")]
    Memory(String),
    
    #[error("GPU error: {0}")]
    Gpu(String),
}

/// Errors related to image processing operations
#[derive(Error, Debug)]
pub enum ProcessingError {
    #[error("Failed to decode image: {0}")]
    DecodeError(String),
    
    #[error("Failed to encode image: {0}")]
    EncodeError(String),
    
    #[error("Invalid image dimensions: width={width}, height={height}")]
    InvalidDimensions { width: u32, height: u32 },
    
    #[error("Unsupported image format: {format}")]
    UnsupportedFormat { format: String },
    
    #[error("Image too large: {size} bytes")]
    ImageTooLarge { size: usize },
    
    #[error("Corrupted image data")]
    CorruptedData,
    
    #[error("Processing timeout after {seconds} seconds")]
    Timeout { seconds: u64 },
    
    #[error("Insufficient memory for processing")]
    InsufficientMemory,
}

/// Errors related to collage creation
#[derive(Error, Debug)]
pub enum CollageError {
    #[error("Invalid number of images: {count} (expected between {min} and {max})")]
    InvalidImageCount { count: usize, min: usize, max: usize },
    
    #[error("Canvas size too small: {width}x{height}")]
    CanvasTooSmall { width: u32, height: u32 },
    
    #[error("Canvas size too large: {width}x{height}")]
    CanvasTooLarge { width: u32, height: u32 },
    
    #[error("Layout generation failed: {reason}")]
    LayoutGenerationFailed { reason: String },
    
    #[error("Image optimization failed for image {index}: {reason}")]
    ImageOptimizationFailed { index: usize, reason: String },
    
    #[error("Composition failed: {reason}")]
    CompositionFailed { reason: String },
    
    #[error("Unsupported layout type: {layout_type}")]
    UnsupportedLayoutType { layout_type: String },
}

/// Errors related to format conversion
#[derive(Error, Debug)]
pub enum ConversionError {
    #[error("Unsupported input format: {format}")]
    UnsupportedInputFormat { format: String },
    
    #[error("Unsupported output format: {format}")]
    UnsupportedOutputFormat { format: String },
    
    #[error("Quality value out of range: {quality} (expected 0-100)")]
    InvalidQuality { quality: u8 },
    
    #[error("Conversion failed: {reason}")]
    ConversionFailed { reason: String },
    
    #[error("Metadata extraction failed: {reason}")]
    MetadataExtractionFailed { reason: String },
    
    #[error("Metadata preservation failed: {reason}")]
    MetadataPreservationFailed { reason: String },
}

/// Errors related to filter application
#[derive(Error, Debug)]
pub enum FilterError {
    #[error("Unknown filter type: {filter_type}")]
    UnknownFilterType { filter_type: String },
    
    #[error("Filter intensity out of range: {intensity} (expected 0.0-1.0)")]
    InvalidIntensity { intensity: f32 },
    
    #[error("Filter parameter invalid: {parameter}={value}")]
    InvalidParameter { parameter: String, value: String },
    
    #[error("Filter application failed: {reason}")]
    ApplicationFailed { reason: String },
    
    #[error("Filter not compatible with image format: {format}")]
    IncompatibleFormat { format: String },
    
    #[error("Filter requires GPU acceleration but it's not available")]
    GpuRequired,
    
    #[error("AI model loading failed: {model_name}")]
    AiModelLoadFailed { model_name: String },
}

/// Result type alias for TuShen operations
pub type TuShenResult<T> = Result<T, TuShenError>;

/// Result type alias for processing operations
pub type ProcessingResult<T> = Result<T, ProcessingError>;

/// Result type alias for collage operations
pub type CollageResult<T> = Result<T, CollageError>;

/// Result type alias for conversion operations
pub type ConversionResult<T> = Result<T, ConversionError>;

/// Result type alias for filter operations
pub type FilterResult<T> = Result<T, FilterError>;

impl TuShenError {
    /// Create an internal error with a custom message
    pub fn internal<S: Into<String>>(message: S) -> Self {
        TuShenError::Internal(message.into())
    }
    
    /// Create an invalid input error with a custom message
    pub fn invalid_input<S: Into<String>>(message: S) -> Self {
        TuShenError::InvalidInput(message.into())
    }
    
    /// Create an unsupported operation error with a custom message
    pub fn unsupported<S: Into<String>>(message: S) -> Self {
        TuShenError::UnsupportedOperation(message.into())
    }
    
    /// Create a memory error with a custom message
    pub fn memory<S: Into<String>>(message: S) -> Self {
        TuShenError::Memory(message.into())
    }
    
    /// Create a GPU error with a custom message
    pub fn gpu<S: Into<String>>(message: S) -> Self {
        TuShenError::Gpu(message.into())
    }
    
    /// Check if this error is recoverable
    pub fn is_recoverable(&self) -> bool {
        match self {
            TuShenError::Processing(ProcessingError::Timeout { .. }) => true,
            TuShenError::Processing(ProcessingError::InsufficientMemory) => true,
            TuShenError::Memory(_) => true,
            TuShenError::Gpu(_) => true,
            _ => false,
        }
    }
    
    /// Get error category for analytics
    pub fn category(&self) -> &'static str {
        match self {
            TuShenError::Processing(_) => "processing",
            TuShenError::Collage(_) => "collage",
            TuShenError::Conversion(_) => "conversion",
            TuShenError::Filter(_) => "filter",
            TuShenError::Io(_) => "io",
            TuShenError::Serialization(_) => "serialization",
            TuShenError::Internal(_) => "internal",
            TuShenError::InvalidInput(_) => "invalid_input",
            TuShenError::UnsupportedOperation(_) => "unsupported",
            TuShenError::Memory(_) => "memory",
            TuShenError::Gpu(_) => "gpu",
        }
    }
    
    /// Convert to a user-friendly error message
    pub fn user_message(&self) -> String {
        match self {
            TuShenError::Processing(ProcessingError::ImageTooLarge { .. }) => {
                "The image is too large to process. Please try with a smaller image.".to_string()
            }
            TuShenError::Processing(ProcessingError::UnsupportedFormat { .. }) => {
                "This image format is not supported.".to_string()
            }
            TuShenError::Processing(ProcessingError::CorruptedData) => {
                "The image file appears to be corrupted.".to_string()
            }
            TuShenError::Processing(ProcessingError::Timeout { .. }) => {
                "Processing is taking too long. Please try again.".to_string()
            }
            TuShenError::Processing(ProcessingError::InsufficientMemory) => {
                "Not enough memory to process this image.".to_string()
            }
            TuShenError::Collage(CollageError::InvalidImageCount { .. }) => {
                "Please select between 2 and 20 images for the collage.".to_string()
            }
            TuShenError::Conversion(ConversionError::UnsupportedOutputFormat { .. }) => {
                "Cannot convert to this format.".to_string()
            }
            TuShenError::Filter(FilterError::GpuRequired) => {
                "This filter requires hardware acceleration which is not available.".to_string()
            }
            TuShenError::Memory(_) => {
                "Not enough memory available. Please close other apps and try again.".to_string()
            }
            TuShenError::Gpu(_) => {
                "Graphics processing error. Please try again.".to_string()
            }
            _ => "An unexpected error occurred. Please try again.".to_string(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let error = TuShenError::internal("test message");
        assert!(matches!(error, TuShenError::Internal(_)));
    }

    #[test]
    fn test_error_category() {
        let error = TuShenError::Processing(ProcessingError::CorruptedData);
        assert_eq!(error.category(), "processing");
    }

    #[test]
    fn test_recoverable_errors() {
        let timeout_error = TuShenError::Processing(ProcessingError::Timeout { seconds: 30 });
        assert!(timeout_error.is_recoverable());
        
        let corrupted_error = TuShenError::Processing(ProcessingError::CorruptedData);
        assert!(!corrupted_error.is_recoverable());
    }

    #[test]
    fn test_user_messages() {
        let error = TuShenError::Processing(ProcessingError::CorruptedData);
        let message = error.user_message();
        assert!(message.contains("corrupted"));
    }
}
