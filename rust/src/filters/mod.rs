//! Image filters module
//! 
//! This module provides various image filters and effects for the TuShen app.

use crate::{
    utils::{ImageData, ImageFormat, ColorSpace},
    error::{TuShenError, FilterError},
};
use image::DynamicImage;
use std::fmt;
use serde::{Serialize, Deserialize};

/// Filter types available in the system
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum FilterType {
    // Basic filters
    Vintage,
    BlackWhite,
    Sepia,
    Blur,
    Sharpen,
    
    // Adjustment filters
    Brightness,
    Contrast,
    Saturation,
    Hue,
    Gamma,
    
    // Artistic filters
    OilPainting,
    Watercolor,
    Sketch,
    Cartoon,
    
    // Portrait filters
    SkinSmooth,
    EyeBrighten,
    TeethWhiten,
    
    // Landscape filters
    SkyEnhance,
    FoliageBoost,
    SunsetWarm,
    
    // Architecture filters
    StructureEnhance,
    PerspectiveCorrect,
    
    // Food filters
    FoodVibrant,
    WarmTone,
    
    // Custom filters
    Custom(String),
}

impl fmt::Display for FilterType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            FilterType::Vintage => write!(f, "vintage"),
            FilterType::BlackWhite => write!(f, "black_white"),
            FilterType::Sepia => write!(f, "sepia"),
            FilterType::Blur => write!(f, "blur"),
            FilterType::Sharpen => write!(f, "sharpen"),
            FilterType::Brightness => write!(f, "brightness"),
            FilterType::Contrast => write!(f, "contrast"),
            FilterType::Saturation => write!(f, "saturation"),
            FilterType::Hue => write!(f, "hue"),
            FilterType::Gamma => write!(f, "gamma"),
            FilterType::OilPainting => write!(f, "oil_painting"),
            FilterType::Watercolor => write!(f, "watercolor"),
            FilterType::Sketch => write!(f, "sketch"),
            FilterType::Cartoon => write!(f, "cartoon"),
            FilterType::SkinSmooth => write!(f, "skin_smooth"),
            FilterType::EyeBrighten => write!(f, "eye_brighten"),
            FilterType::TeethWhiten => write!(f, "teeth_whiten"),
            FilterType::SkyEnhance => write!(f, "sky_enhance"),
            FilterType::FoliageBoost => write!(f, "foliage_boost"),
            FilterType::SunsetWarm => write!(f, "sunset_warm"),
            FilterType::StructureEnhance => write!(f, "structure_enhance"),
            FilterType::PerspectiveCorrect => write!(f, "perspective_correct"),
            FilterType::FoodVibrant => write!(f, "food_vibrant"),
            FilterType::WarmTone => write!(f, "warm_tone"),
            FilterType::Custom(name) => write!(f, "custom_{}", name),
        }
    }
}

/// Filter categories for organization
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum FilterCategory {
    Basic,
    Adjustment,
    Artistic,
    Portrait,
    Landscape,
    Architecture,
    Food,
    Custom,
}

/// Main filter engine
pub struct FilterEngine {
    // Filter cache and optimization data
}

impl FilterEngine {
    /// Create a new filter engine
    pub fn new() -> Self {
        Self {}
    }

    /// Apply a filter to an image
    pub async fn apply_filter(
        &self,
        image: ImageData,
        filter_type: FilterType,
        intensity: f32,
    ) -> Result<ImageData, TuShenError> {
        let intensity = intensity.clamp(0.0, 1.0);
        
        // Convert ImageData to DynamicImage
        let dynamic_image = self.image_data_to_dynamic_image(&image)?;
        
        // Apply the filter
        let filtered_image = match filter_type {
            FilterType::Vintage => self.apply_vintage_filter(dynamic_image, intensity),
            FilterType::BlackWhite => self.apply_black_white_filter(dynamic_image, intensity),
            FilterType::Sepia => self.apply_sepia_filter(dynamic_image, intensity),
            FilterType::Blur => self.apply_blur_filter(dynamic_image, intensity),
            FilterType::Sharpen => self.apply_sharpen_filter(dynamic_image, intensity),
            FilterType::Brightness => self.apply_brightness_filter(dynamic_image, intensity),
            FilterType::Contrast => self.apply_contrast_filter(dynamic_image, intensity),
            FilterType::Saturation => self.apply_saturation_filter(dynamic_image, intensity),
            _ => return Err(TuShenError::Filter(FilterError::UnknownFilterType { filter_type: filter_type.to_string() })),
        }?;

        // Convert back to ImageData
        self.dynamic_image_to_image_data(filtered_image, image.format)
    }

    /// Get available filters
    pub fn get_available_filters(&self) -> Vec<FilterType> {
        vec![
            FilterType::Vintage,
            FilterType::BlackWhite,
            FilterType::Sepia,
            FilterType::Blur,
            FilterType::Sharpen,
            FilterType::Brightness,
            FilterType::Contrast,
            FilterType::Saturation,
        ]
    }

    /// Get filter preview (smaller size for performance)
    pub async fn get_preview(
        &self,
        image: ImageData,
        filter_type: FilterType,
        preview_size: u32,
    ) -> Result<ImageData, TuShenError> {
        // Create a smaller version for preview
        let dynamic_image = self.image_data_to_dynamic_image(&image)?;
        let preview_image = dynamic_image.resize(
            preview_size,
            preview_size,
            image::imageops::FilterType::Lanczos3,
        );

        // Convert to ImageData and apply filter
        let preview_data = self.dynamic_image_to_image_data(preview_image, image.format)?;
        self.apply_filter(preview_data, filter_type, 1.0).await
    }

    // Filter implementations
    fn apply_vintage_filter(&self, image: DynamicImage, intensity: f32) -> Result<DynamicImage, TuShenError> {
        let mut img = image.to_rgba8();
        
        for pixel in img.pixels_mut() {
            let [r, g, b, a] = pixel.0;
            
            // Vintage effect: warm tones, reduced contrast
            let new_r = ((r as f32 * 1.2 + g as f32 * 0.1) * intensity + r as f32 * (1.0 - intensity)).min(255.0) as u8;
            let new_g = ((g as f32 * 1.1 + r as f32 * 0.05) * intensity + g as f32 * (1.0 - intensity)).min(255.0) as u8;
            let new_b = ((b as f32 * 0.8) * intensity + b as f32 * (1.0 - intensity)).min(255.0) as u8;
            
            pixel.0 = [new_r, new_g, new_b, a];
        }
        
        Ok(DynamicImage::ImageRgba8(img))
    }

    fn apply_black_white_filter(&self, image: DynamicImage, intensity: f32) -> Result<DynamicImage, TuShenError> {
        let mut img = image.to_rgba8();
        
        for pixel in img.pixels_mut() {
            let [r, g, b, a] = pixel.0;
            
            // Standard grayscale conversion
            let gray = (0.299 * r as f32 + 0.587 * g as f32 + 0.114 * b as f32) as u8;
            
            let new_r = (gray as f32 * intensity + r as f32 * (1.0 - intensity)) as u8;
            let new_g = (gray as f32 * intensity + g as f32 * (1.0 - intensity)) as u8;
            let new_b = (gray as f32 * intensity + b as f32 * (1.0 - intensity)) as u8;
            
            pixel.0 = [new_r, new_g, new_b, a];
        }
        
        Ok(DynamicImage::ImageRgba8(img))
    }

    fn apply_sepia_filter(&self, image: DynamicImage, intensity: f32) -> Result<DynamicImage, TuShenError> {
        let mut img = image.to_rgba8();
        
        for pixel in img.pixels_mut() {
            let [r, g, b, a] = pixel.0;
            
            // Sepia transformation matrix
            let new_r = ((r as f32 * 0.393 + g as f32 * 0.769 + b as f32 * 0.189) * intensity + r as f32 * (1.0 - intensity)).min(255.0) as u8;
            let new_g = ((r as f32 * 0.349 + g as f32 * 0.686 + b as f32 * 0.168) * intensity + g as f32 * (1.0 - intensity)).min(255.0) as u8;
            let new_b = ((r as f32 * 0.272 + g as f32 * 0.534 + b as f32 * 0.131) * intensity + b as f32 * (1.0 - intensity)).min(255.0) as u8;
            
            pixel.0 = [new_r, new_g, new_b, a];
        }
        
        Ok(DynamicImage::ImageRgba8(img))
    }

    fn apply_blur_filter(&self, image: DynamicImage, intensity: f32) -> Result<DynamicImage, TuShenError> {
        let sigma = intensity * 5.0; // Max blur radius of 5
        Ok(image.blur(sigma))
    }

    fn apply_sharpen_filter(&self, image: DynamicImage, intensity: f32) -> Result<DynamicImage, TuShenError> {
        // Simple sharpening using unsharp mask
        let blurred = image.blur(1.0);
        let mut result = image.to_rgba8();
        let blurred = blurred.to_rgba8();
        
        for (original, blurred_pixel) in result.pixels_mut().zip(blurred.pixels()) {
            let [r, g, b, a] = original.0;
            let [br, bg, bb, _] = blurred_pixel.0;
            
            let amount = intensity * 2.0;
            let new_r = ((r as f32 + amount * (r as f32 - br as f32)).max(0.0).min(255.0)) as u8;
            let new_g = ((g as f32 + amount * (g as f32 - bg as f32)).max(0.0).min(255.0)) as u8;
            let new_b = ((b as f32 + amount * (b as f32 - bb as f32)).max(0.0).min(255.0)) as u8;
            
            original.0 = [new_r, new_g, new_b, a];
        }
        
        Ok(DynamicImage::ImageRgba8(result))
    }

    fn apply_brightness_filter(&self, image: DynamicImage, intensity: f32) -> Result<DynamicImage, TuShenError> {
        let adjustment = (intensity - 0.5) * 100.0; // -50 to +50
        Ok(image.brighten(adjustment as i32))
    }

    fn apply_contrast_filter(&self, image: DynamicImage, intensity: f32) -> Result<DynamicImage, TuShenError> {
        let contrast = intensity * 2.0; // 0 to 2.0
        let mut img = image.to_rgba8();
        
        for pixel in img.pixels_mut() {
            let [r, g, b, a] = pixel.0;
            
            let new_r = (((r as f32 / 255.0 - 0.5) * contrast + 0.5) * 255.0).max(0.0).min(255.0) as u8;
            let new_g = (((g as f32 / 255.0 - 0.5) * contrast + 0.5) * 255.0).max(0.0).min(255.0) as u8;
            let new_b = (((b as f32 / 255.0 - 0.5) * contrast + 0.5) * 255.0).max(0.0).min(255.0) as u8;
            
            pixel.0 = [new_r, new_g, new_b, a];
        }
        
        Ok(DynamicImage::ImageRgba8(img))
    }

    fn apply_saturation_filter(&self, image: DynamicImage, intensity: f32) -> Result<DynamicImage, TuShenError> {
        let saturation = intensity * 2.0; // 0 to 2.0
        let mut img = image.to_rgba8();
        
        for pixel in img.pixels_mut() {
            let [r, g, b, a] = pixel.0;
            
            // Convert to HSV, adjust saturation, convert back
            let gray = (0.299 * r as f32 + 0.587 * g as f32 + 0.114 * b as f32) / 255.0;
            
            let new_r = ((gray + (r as f32 / 255.0 - gray) * saturation) * 255.0).max(0.0).min(255.0) as u8;
            let new_g = ((gray + (g as f32 / 255.0 - gray) * saturation) * 255.0).max(0.0).min(255.0) as u8;
            let new_b = ((gray + (b as f32 / 255.0 - gray) * saturation) * 255.0).max(0.0).min(255.0) as u8;
            
            pixel.0 = [new_r, new_g, new_b, a];
        }
        
        Ok(DynamicImage::ImageRgba8(img))
    }

    // Helper methods
    fn image_data_to_dynamic_image(&self, image_data: &ImageData) -> Result<DynamicImage, TuShenError> {
        image::load_from_memory(&image_data.data)
            .map_err(|e| TuShenError::Filter(FilterError::ApplicationFailed { reason: e.to_string() }))
    }

    fn dynamic_image_to_image_data(&self, image: DynamicImage, format: ImageFormat) -> Result<ImageData, TuShenError> {
        let mut buffer = Vec::new();
        let image_format = match format {
            ImageFormat::Jpeg => image::ImageFormat::Jpeg,
            ImageFormat::Png => image::ImageFormat::Png,
            ImageFormat::WebP => image::ImageFormat::WebP,
            ImageFormat::Gif => image::ImageFormat::Gif,
            ImageFormat::Bmp => image::ImageFormat::Bmp,
            ImageFormat::Tiff => image::ImageFormat::Tiff,
            _ => image::ImageFormat::Jpeg, // Default fallback
        };

        image.write_to(&mut std::io::Cursor::new(&mut buffer), image_format)
            .map_err(|e| TuShenError::Filter(FilterError::ApplicationFailed { reason: e.to_string() }))?;

        Ok(ImageData {
            width: image.width(),
            height: image.height(),
            format,
            color_space: ColorSpace::Srgb,
            metadata: None,
            data: buffer,
        })
    }
}

// Specialized filter implementations
pub struct PortraitFilter;
pub struct LandscapeFilter;
pub struct ArchitectureFilter;
pub struct FoodFilter;
pub struct ArtisticFilter;

impl Default for FilterEngine {
    fn default() -> Self {
        Self::new()
    }
}
