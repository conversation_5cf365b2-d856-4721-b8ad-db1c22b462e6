{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 11738465886959544573, "path": 8334373190912973897, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/weezl-5d392082f1dfe8ce/dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}