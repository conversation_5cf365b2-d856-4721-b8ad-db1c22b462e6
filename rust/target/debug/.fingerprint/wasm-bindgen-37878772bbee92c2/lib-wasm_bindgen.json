{"rustc": 15497389221046826682, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\", \"strict-macro\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 12554258093832455865, "path": 3362277061376465112, "deps": [[2828590642173593838, "cfg_if", false, 15914678172947282822], [3722963349756955755, "once_cell", false, 15886205355209086610], [6946689283190175495, "build_script_build", false, 786964867694266594], [7858942147296547339, "rustversion", false, 12508874185770479194], [11382113702854245495, "wasm_bindgen_macro", false, 16268127316535412005]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-37878772bbee92c2/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}