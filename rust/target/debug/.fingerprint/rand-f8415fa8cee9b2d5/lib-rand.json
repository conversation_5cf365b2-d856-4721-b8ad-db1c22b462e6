{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 8827111241893198906, "profile": 11738465886959544573, "path": 11448770889543529959, "deps": [[1333041802001714747, "rand_chacha", false, 7781827179896712586], [1740877332521282793, "rand_core", false, 10156009680500814122], [4684437522915235464, "libc", false, 6041144207319583681], [5170503507811329045, "getrandom_package", false, 14459845989807778535]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-f8415fa8cee9b2d5/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}