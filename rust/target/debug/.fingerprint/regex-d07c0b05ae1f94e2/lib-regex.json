{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 155454362548387972, "path": 7478926993460006388, "deps": [[555019317135488525, "regex_automata", false, 626993140943070865], [9408802513701742484, "regex_syntax", false, 6059302938765117581]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-d07c0b05ae1f94e2/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}