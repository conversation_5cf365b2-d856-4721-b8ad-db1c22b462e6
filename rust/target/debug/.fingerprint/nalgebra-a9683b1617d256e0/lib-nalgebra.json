{"rustc": 15497389221046826682, "features": "[\"matrixmultiply\", \"std\"]", "declared_features": "[\"abomonation\", \"abomonation-serialize\", \"alga\", \"alloc\", \"arbitrary\", \"bytemuck\", \"compare\", \"convert-bytemuck\", \"convert-glam013\", \"convert-glam014\", \"convert-glam015\", \"convert-glam016\", \"convert-glam017\", \"convert-glam018\", \"convert-glam019\", \"convert-glam020\", \"convert-mint\", \"cuda\", \"cust\", \"debug\", \"default\", \"glam013\", \"glam014\", \"glam015\", \"glam016\", \"glam017\", \"glam018\", \"glam019\", \"glam020\", \"io\", \"libm\", \"libm-force\", \"macros\", \"matrixcompare-core\", \"matrixmultiply\", \"mint\", \"nalgebra-macros\", \"pest\", \"pest_derive\", \"proptest\", \"proptest-support\", \"quickcheck\", \"rand\", \"rand-no-std\", \"rand-package\", \"rand_distr\", \"rkyv\", \"rkyv-serialize\", \"rkyv-serialize-no-std\", \"serde\", \"serde-serialize\", \"serde-serialize-no-std\", \"slow-tests\", \"sparse\", \"std\"]", "target": 572955357253318494, "profile": 11738465886959544573, "path": 2697808431475409063, "deps": [[2819946551904607991, "num_rational", false, 9836370343272685650], [5157631553186200874, "num_traits", false, 2558546937163958804], [12319020793864570031, "num_complex", false, 16871611679943132334], [15677050387741058262, "approx", false, 10342257010929964894], [15826188163127377936, "matrixmultiply", false, 9994777256301358366], [17001665395952474378, "typenum", false, 12997064264013575161], [17877237321909315803, "simba", false, 3545815902482409033]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nalgebra-a9683b1617d256e0/dep-lib-nalgebra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}