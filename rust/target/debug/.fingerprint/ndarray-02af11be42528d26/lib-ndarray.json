{"rustc": 15497389221046826682, "features": "[\"default\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 155454362548387972, "path": 6608116315594454299, "deps": [[5157631553186200874, "num_traits", false, 9718211184153599619], [12319020793864570031, "num_complex", false, 11885534461338933419], [15709748443193639506, "rawpointer", false, 12079828112813320940], [15826188163127377936, "matrixmultiply", false, 7742171072974914764], [16795989132585092538, "num_integer", false, 4145566647333179490]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ndarray-02af11be42528d26/dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}