{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 11738465886959544573, "path": 7478926993460006388, "deps": [[555019317135488525, "regex_automata", false, 4948319644102870012], [9408802513701742484, "regex_syntax", false, 10614983202873170334]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-59000b3e7f4a8b54/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}