{"rustc": 15497389221046826682, "features": "[\"alloc\", \"approx\", \"default\", \"named\", \"named_from_str\", \"phf\", \"std\"]", "declared_features": "[\"alloc\", \"approx\", \"bytemuck\", \"default\", \"find-crate\", \"libm\", \"named\", \"named_from_str\", \"phf\", \"rand\", \"random\", \"serde\", \"serializing\", \"std\", \"wide\"]", "target": 6519571351683251528, "profile": 155454362548387972, "path": 7021302038586041100, "deps": [[3333774257163257407, "fast_srgb8", false, 13093115054002827693], [5055421393008045653, "palette_derive", false, 7993210019650896726], [12385318719625307482, "build_script_main", false, 11682748432576558455], [15677050387741058262, "approx", false, 16025494233993693827], [17186037756130803222, "phf", false, 9667462745544652907]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/palette-e61bd8875946e03d/dep-lib-palette", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}