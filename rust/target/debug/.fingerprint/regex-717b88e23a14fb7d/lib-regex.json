{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 155454362548387972, "path": 7478926993460006388, "deps": [[555019317135488525, "regex_automata", false, 3929713846122870031], [9408802513701742484, "regex_syntax", false, 3646377069389303565]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-717b88e23a14fb7d/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}