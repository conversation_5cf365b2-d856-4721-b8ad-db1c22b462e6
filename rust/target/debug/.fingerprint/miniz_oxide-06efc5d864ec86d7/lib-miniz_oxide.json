{"rustc": 15497389221046826682, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 14141058377156111734, "path": 4839982442373102788, "deps": [[4018467389006652250, "simd_adler32", false, 15580456858718478272], [7911289239703230891, "adler2", false, 5080155245693636513]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/miniz_oxide-06efc5d864ec86d7/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}