{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 8549343027129265702, "path": 4114963990500894564, "deps": [[5103565458935487, "futures_io", false, 14259608171603821081], [1811549171721445101, "futures_channel", false, 1938525957474956539], [7013762810557009322, "futures_sink", false, 17892929542056556994], [7620660491849607393, "futures_core", false, 15727088182970171476], [10629569228670356391, "futures_util", false, 8181375293834153040], [12779779637805422465, "futures_executor", false, 6467957716479146046], [16240732885093539806, "futures_task", false, 1883897024605028577]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-2e75adac45ae931a/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}