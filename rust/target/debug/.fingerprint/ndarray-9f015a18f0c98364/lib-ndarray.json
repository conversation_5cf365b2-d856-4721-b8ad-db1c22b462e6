{"rustc": 15497389221046826682, "features": "[\"default\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 11738465886959544573, "path": 6608116315594454299, "deps": [[5157631553186200874, "num_traits", false, 2558546937163958804], [12319020793864570031, "num_complex", false, 16871611679943132334], [15709748443193639506, "rawpointer", false, 14298279361552014251], [15826188163127377936, "matrixmultiply", false, 9994777256301358366], [16795989132585092538, "num_integer", false, 13503833647121968626]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ndarray-9f015a18f0c98364/dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}