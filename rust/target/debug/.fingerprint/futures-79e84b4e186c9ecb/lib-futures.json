{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 3202114818891730933, "path": 4114963990500894564, "deps": [[5103565458935487, "futures_io", false, 13960816600426139235], [1811549171721445101, "futures_channel", false, 11706863403767831039], [7013762810557009322, "futures_sink", false, 4555711867105049736], [7620660491849607393, "futures_core", false, 11256263443429150832], [10629569228670356391, "futures_util", false, 1670931260399408789], [12779779637805422465, "futures_executor", false, 437370524845595400], [16240732885093539806, "futures_task", false, 3151191565029276732]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-79e84b4e186c9ecb/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}