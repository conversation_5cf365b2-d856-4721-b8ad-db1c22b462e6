{"rustc": 15497389221046826682, "features": "[\"default\", \"macros\", \"matrixmultiply\", \"nalgebra-macros\", \"std\"]", "declared_features": "[\"alga\", \"alloc\", \"arbitrary\", \"bytemuck\", \"compare\", \"convert-bytemuck\", \"convert-glam014\", \"convert-glam015\", \"convert-glam016\", \"convert-glam017\", \"convert-glam018\", \"convert-glam019\", \"convert-glam020\", \"convert-glam021\", \"convert-glam022\", \"convert-glam023\", \"convert-glam024\", \"convert-glam025\", \"convert-glam027\", \"convert-mint\", \"cuda\", \"cust_core\", \"debug\", \"default\", \"glam014\", \"glam015\", \"glam016\", \"glam017\", \"glam018\", \"glam019\", \"glam020\", \"glam021\", \"glam022\", \"glam023\", \"glam024\", \"glam025\", \"glam027\", \"io\", \"libm\", \"libm-force\", \"macros\", \"matrixcompare-core\", \"matrixmultiply\", \"mint\", \"nalgebra-macros\", \"pest\", \"pest_derive\", \"proptest\", \"proptest-support\", \"quickcheck\", \"rand\", \"rand-no-std\", \"rand-package\", \"rand_distr\", \"rayon\", \"rkyv\", \"rkyv-safe-deser\", \"rkyv-serialize\", \"rkyv-serialize-no-std\", \"serde\", \"serde-serialize\", \"serde-serialize-no-std\", \"slow-tests\", \"sparse\", \"std\"]", "target": 572955357253318494, "profile": 155454362548387972, "path": 12494730964156036833, "deps": [[2819946551904607991, "num_rational", false, 50816800223554143], [4462856585586636430, "simba", false, 1606000192954871763], [5157631553186200874, "num_traits", false, 9718211184153599619], [11394677342629719743, "nalgebra_macros", false, 3559808248669916073], [12319020793864570031, "num_complex", false, 11885534461338933419], [15677050387741058262, "approx", false, 16025494233993693827], [15826188163127377936, "matrixmultiply", false, 7742171072974914764], [17001665395952474378, "typenum", false, 3501147893500048420]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nalgebra-2b1a9b576660eb7f/dep-lib-nalgebra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}