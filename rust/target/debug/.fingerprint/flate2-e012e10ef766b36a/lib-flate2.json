{"rustc": 15497389221046826682, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 11738465886959544573, "path": 7533416816687553690, "deps": [[5466618496199522463, "crc32fast", false, 2890184185967714649], [7636735136738807108, "miniz_oxide", false, 9550470064749477653]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-e012e10ef766b36a/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}