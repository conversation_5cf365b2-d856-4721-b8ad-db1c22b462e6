{"rustc": 15497389221046826682, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 15315924755136109342, "profile": 155454362548387972, "path": 17674897711297674729, "deps": [[5157631553186200874, "num_traits", false, 9718211184153599619], [7910860254152155345, "iana_time_zone", false, 3842427387389838780], [9689903380558560274, "serde", false, 17388662994599467583]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/chrono-f0872d5e5bdc7f9c/dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}