{"rustc": 15497389221046826682, "features": "[\"default\", \"macros\", \"matrixmultiply\", \"nalgebra-macros\", \"std\"]", "declared_features": "[\"alga\", \"alloc\", \"arbitrary\", \"bytemuck\", \"compare\", \"convert-bytemuck\", \"convert-glam014\", \"convert-glam015\", \"convert-glam016\", \"convert-glam017\", \"convert-glam018\", \"convert-glam019\", \"convert-glam020\", \"convert-glam021\", \"convert-glam022\", \"convert-glam023\", \"convert-glam024\", \"convert-glam025\", \"convert-glam027\", \"convert-mint\", \"cuda\", \"cust_core\", \"debug\", \"default\", \"glam014\", \"glam015\", \"glam016\", \"glam017\", \"glam018\", \"glam019\", \"glam020\", \"glam021\", \"glam022\", \"glam023\", \"glam024\", \"glam025\", \"glam027\", \"io\", \"libm\", \"libm-force\", \"macros\", \"matrixcompare-core\", \"matrixmultiply\", \"mint\", \"nalgebra-macros\", \"pest\", \"pest_derive\", \"proptest\", \"proptest-support\", \"quickcheck\", \"rand\", \"rand-no-std\", \"rand-package\", \"rand_distr\", \"rayon\", \"rkyv\", \"rkyv-safe-deser\", \"rkyv-serialize\", \"rkyv-serialize-no-std\", \"serde\", \"serde-serialize\", \"serde-serialize-no-std\", \"slow-tests\", \"sparse\", \"std\"]", "target": 572955357253318494, "profile": 11738465886959544573, "path": 12494730964156036833, "deps": [[2819946551904607991, "num_rational", false, 9836370343272685650], [4462856585586636430, "simba", false, 1299149490373321357], [5157631553186200874, "num_traits", false, 2558546937163958804], [11394677342629719743, "nalgebra_macros", false, 3559808248669916073], [12319020793864570031, "num_complex", false, 16871611679943132334], [15677050387741058262, "approx", false, 10342257010929964894], [15826188163127377936, "matrixmultiply", false, 9994777256301358366], [17001665395952474378, "typenum", false, 12997064264013575161]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nalgebra-c806dcbaf8965be4/dep-lib-nalgebra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}