{"rustc": 15497389221046826682, "features": "[\"alloc\", \"approx\", \"default\", \"named\", \"named_from_str\", \"phf\", \"std\"]", "declared_features": "[\"alloc\", \"approx\", \"bytemuck\", \"default\", \"find-crate\", \"libm\", \"named\", \"named_from_str\", \"phf\", \"rand\", \"random\", \"serde\", \"serializing\", \"std\", \"wide\"]", "target": 6519571351683251528, "profile": 11738465886959544573, "path": 7021302038586041100, "deps": [[3333774257163257407, "fast_srgb8", false, 14788567284439772621], [5055421393008045653, "palette_derive", false, 7993210019650896726], [12385318719625307482, "build_script_main", false, 11682748432576558455], [15677050387741058262, "approx", false, 10342257010929964894], [17186037756130803222, "phf", false, 10433337124463369013]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/palette-3953dfe6c69b8f86/dep-lib-palette", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}