{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 7850165480177918260, "path": 6016627951566501110, "deps": [[14814905555676593471, "clap_builder", false, 3621198004356249931]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-af290c0d9f32dbaa/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}