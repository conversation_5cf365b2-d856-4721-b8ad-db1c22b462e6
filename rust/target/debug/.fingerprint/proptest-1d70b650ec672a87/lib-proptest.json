{"rustc": 15497389221046826682, "features": "[\"bit-set\", \"default\", \"fork\", \"lazy_static\", \"regex-syntax\", \"rusty-fork\", \"std\", \"tempfile\", \"timeout\"]", "declared_features": "[\"alloc\", \"atomic64bit\", \"attr-macro\", \"bit-set\", \"default\", \"default-code-coverage\", \"fork\", \"handle-panics\", \"hardware-rng\", \"lazy_static\", \"no_std\", \"proptest-macro\", \"regex-syntax\", \"rusty-fork\", \"std\", \"tempfile\", \"timeout\", \"unstable\", \"x86\"]", "target": 8368435328612947345, "profile": 155454362548387972, "path": 4359370922192564789, "deps": [[1441306149310335789, "tempfile", false, 14377388069224858645], [2098583196738611028, "rand", false, 5183005092349520244], [4344686646292094751, "rusty_fork", false, 16449291822953035699], [5157631553186200874, "num_traits", false, 9718211184153599619], [5652558058897858086, "rand_chacha", false, 4020862078605336339], [5692597712387868707, "bit_vec", false, 2137056502577257537], [7896293946984509699, "bitflags", false, 17585506198609535855], [9408802513701742484, "regex_syntax", false, 3646377069389303565], [9519969280819313548, "bit_set", false, 15723778837769216763], [14014736296291115408, "unarray", false, 3656846245399984853], [15141648066790386875, "rand_xorshift", false, 6914610162566953835], [17917672826516349275, "lazy_static", false, 1241073817942100244]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/proptest-1d70b650ec672a87/dep-lib-proptest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}