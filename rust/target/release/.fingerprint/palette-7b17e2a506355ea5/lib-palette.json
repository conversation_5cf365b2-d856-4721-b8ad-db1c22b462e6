{"rustc": 15497389221046826682, "features": "[\"alloc\", \"approx\", \"default\", \"named\", \"named_from_str\", \"phf\", \"std\"]", "declared_features": "[\"alloc\", \"approx\", \"bytemuck\", \"default\", \"find-crate\", \"libm\", \"named\", \"named_from_str\", \"phf\", \"rand\", \"random\", \"serde\", \"serializing\", \"std\", \"wide\"]", "target": 6519571351683251528, "profile": 5676177281124120482, "path": 7021302038586041100, "deps": [[3333774257163257407, "fast_srgb8", false, 17055655337571269547], [5055421393008045653, "palette_derive", false, 16990497276661618891], [12385318719625307482, "build_script_main", false, 11410594975548760547], [15677050387741058262, "approx", false, 14204373592303558766], [17186037756130803222, "phf", false, 12478367856487980204]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/palette-7b17e2a506355ea5/dep-lib-palette", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}