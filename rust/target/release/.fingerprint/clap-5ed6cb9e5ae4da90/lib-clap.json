{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 11332462681380664355, "path": 6016627951566501110, "deps": [[14814905555676593471, "clap_builder", false, 6836638455104412662]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap-5ed6cb9e5ae4da90/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}