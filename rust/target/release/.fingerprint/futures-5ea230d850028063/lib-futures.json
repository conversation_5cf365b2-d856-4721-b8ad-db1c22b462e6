{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18134297140301713016, "path": 4114963990500894564, "deps": [[5103565458935487, "futures_io", false, 1450671668879024091], [1811549171721445101, "futures_channel", false, 12911644625645957249], [7013762810557009322, "futures_sink", false, 16849240406833121641], [7620660491849607393, "futures_core", false, 13326438206612714404], [10629569228670356391, "futures_util", false, 13285054071466618941], [12779779637805422465, "futures_executor", false, 9437922401799290182], [16240732885093539806, "futures_task", false, 5613372340315063251]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/futures-5ea230d850028063/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}