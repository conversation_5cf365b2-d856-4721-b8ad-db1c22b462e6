{"rustc": 15497389221046826682, "features": "[\"default\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 5676177281124120482, "path": 6608116315594454299, "deps": [[5157631553186200874, "num_traits", false, 3806966549479256212], [12319020793864570031, "num_complex", false, 14759122239874624272], [15709748443193639506, "rawpointer", false, 7737002745610922781], [15826188163127377936, "matrixmultiply", false, 18445430973907104676], [16795989132585092538, "num_integer", false, 18391538127006810966]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/ndarray-9b525b52746c0d29/dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}