
///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: aliceblue;"></div>
pub const ALICEBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(240, 248, 255);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: antiquewhite;"></div>
pub const ANTIQUEWHITE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(250, 235, 215);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: aqua;"></div>
pub const AQUA: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 255, 255);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: aquamarine;"></div>
pub const AQUAMARINE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(127, 255, 212);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: azure;"></div>
pub const AZURE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(240, 255, 255);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: beige;"></div>
pub const BEIGE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(245, 245, 220);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: bisque;"></div>
pub const BISQUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 228, 196);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: black;"></div>
pub const BLACK: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 0, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: blanchedalmond;"></div>
pub const BLANCHEDALMOND: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 235, 205);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: blue;"></div>
pub const BLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 0, 255);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: blueviolet;"></div>
pub const BLUEVIOLET: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(138, 43, 226);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: brown;"></div>
pub const BROWN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(165, 42, 42);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: burlywood;"></div>
pub const BURLYWOOD: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(222, 184, 135);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: cadetblue;"></div>
pub const CADETBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(95, 158, 160);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: chartreuse;"></div>
pub const CHARTREUSE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(127, 255, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: chocolate;"></div>
pub const CHOCOLATE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(210, 105, 30);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: coral;"></div>
pub const CORAL: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 127, 80);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: cornflowerblue;"></div>
pub const CORNFLOWERBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(100, 149, 237);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: cornsilk;"></div>
pub const CORNSILK: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 248, 220);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: crimson;"></div>
pub const CRIMSON: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(220, 20, 60);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: cyan;"></div>
pub const CYAN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 255, 255);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkblue;"></div>
pub const DARKBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 0, 139);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkcyan;"></div>
pub const DARKCYAN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 139, 139);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkgoldenrod;"></div>
pub const DARKGOLDENROD: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(184, 134, 11);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkgray;"></div>
pub const DARKGRAY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(169, 169, 169);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkgreen;"></div>
pub const DARKGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 100, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkgrey;"></div>
pub const DARKGREY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(169, 169, 169);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkkhaki;"></div>
pub const DARKKHAKI: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(189, 183, 107);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkmagenta;"></div>
pub const DARKMAGENTA: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(139, 0, 139);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkolivegreen;"></div>
pub const DARKOLIVEGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(85, 107, 47);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkorange;"></div>
pub const DARKORANGE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 140, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkorchid;"></div>
pub const DARKORCHID: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(153, 50, 204);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkred;"></div>
pub const DARKRED: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(139, 0, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darksalmon;"></div>
pub const DARKSALMON: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(233, 150, 122);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkseagreen;"></div>
pub const DARKSEAGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(143, 188, 143);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkslateblue;"></div>
pub const DARKSLATEBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(72, 61, 139);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkslategray;"></div>
pub const DARKSLATEGRAY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(47, 79, 79);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkslategrey;"></div>
pub const DARKSLATEGREY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(47, 79, 79);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkturquoise;"></div>
pub const DARKTURQUOISE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 206, 209);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: darkviolet;"></div>
pub const DARKVIOLET: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(148, 0, 211);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: deeppink;"></div>
pub const DEEPPINK: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 20, 147);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: deepskyblue;"></div>
pub const DEEPSKYBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 191, 255);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: dimgray;"></div>
pub const DIMGRAY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(105, 105, 105);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: dimgrey;"></div>
pub const DIMGREY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(105, 105, 105);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: dodgerblue;"></div>
pub const DODGERBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(30, 144, 255);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: firebrick;"></div>
pub const FIREBRICK: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(178, 34, 34);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: floralwhite;"></div>
pub const FLORALWHITE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 250, 240);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: forestgreen;"></div>
pub const FORESTGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(34, 139, 34);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: fuchsia;"></div>
pub const FUCHSIA: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 0, 255);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: gainsboro;"></div>
pub const GAINSBORO: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(220, 220, 220);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: ghostwhite;"></div>
pub const GHOSTWHITE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(248, 248, 255);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: gold;"></div>
pub const GOLD: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 215, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: goldenrod;"></div>
pub const GOLDENROD: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(218, 165, 32);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: gray;"></div>
pub const GRAY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(128, 128, 128);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: grey;"></div>
pub const GREY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(128, 128, 128);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: green;"></div>
pub const GREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 128, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: greenyellow;"></div>
pub const GREENYELLOW: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(173, 255, 47);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: honeydew;"></div>
pub const HONEYDEW: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(240, 255, 240);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: hotpink;"></div>
pub const HOTPINK: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 105, 180);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: indianred;"></div>
pub const INDIANRED: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(205, 92, 92);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: indigo;"></div>
pub const INDIGO: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(75, 0, 130);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: ivory;"></div>
pub const IVORY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 255, 240);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: khaki;"></div>
pub const KHAKI: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(240, 230, 140);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lavender;"></div>
pub const LAVENDER: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(230, 230, 250);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lavenderblush;"></div>
pub const LAVENDERBLUSH: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 240, 245);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lawngreen;"></div>
pub const LAWNGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(124, 252, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lemonchiffon;"></div>
pub const LEMONCHIFFON: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 250, 205);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightblue;"></div>
pub const LIGHTBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(173, 216, 230);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightcoral;"></div>
pub const LIGHTCORAL: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(240, 128, 128);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightcyan;"></div>
pub const LIGHTCYAN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(224, 255, 255);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightgoldenrodyellow;"></div>
pub const LIGHTGOLDENRODYELLOW: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(250, 250, 210);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightgray;"></div>
pub const LIGHTGRAY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(211, 211, 211);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightgreen;"></div>
pub const LIGHTGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(144, 238, 144);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightgrey;"></div>
pub const LIGHTGREY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(211, 211, 211);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightpink;"></div>
pub const LIGHTPINK: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 182, 193);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightsalmon;"></div>
pub const LIGHTSALMON: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 160, 122);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightseagreen;"></div>
pub const LIGHTSEAGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(32, 178, 170);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightskyblue;"></div>
pub const LIGHTSKYBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(135, 206, 250);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightslategray;"></div>
pub const LIGHTSLATEGRAY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(119, 136, 153);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightslategrey;"></div>
pub const LIGHTSLATEGREY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(119, 136, 153);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightsteelblue;"></div>
pub const LIGHTSTEELBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(176, 196, 222);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lightyellow;"></div>
pub const LIGHTYELLOW: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 255, 224);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: lime;"></div>
pub const LIME: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 255, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: limegreen;"></div>
pub const LIMEGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(50, 205, 50);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: linen;"></div>
pub const LINEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(250, 240, 230);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: magenta;"></div>
pub const MAGENTA: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 0, 255);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: maroon;"></div>
pub const MAROON: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(128, 0, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: mediumaquamarine;"></div>
pub const MEDIUMAQUAMARINE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(102, 205, 170);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: mediumblue;"></div>
pub const MEDIUMBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 0, 205);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: mediumorchid;"></div>
pub const MEDIUMORCHID: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(186, 85, 211);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: mediumpurple;"></div>
pub const MEDIUMPURPLE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(147, 112, 219);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: mediumseagreen;"></div>
pub const MEDIUMSEAGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(60, 179, 113);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: mediumslateblue;"></div>
pub const MEDIUMSLATEBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(123, 104, 238);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: mediumspringgreen;"></div>
pub const MEDIUMSPRINGGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 250, 154);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: mediumturquoise;"></div>
pub const MEDIUMTURQUOISE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(72, 209, 204);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: mediumvioletred;"></div>
pub const MEDIUMVIOLETRED: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(199, 21, 133);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: midnightblue;"></div>
pub const MIDNIGHTBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(25, 25, 112);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: mintcream;"></div>
pub const MINTCREAM: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(245, 255, 250);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: mistyrose;"></div>
pub const MISTYROSE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 228, 225);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: moccasin;"></div>
pub const MOCCASIN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 228, 181);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: navajowhite;"></div>
pub const NAVAJOWHITE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 222, 173);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: navy;"></div>
pub const NAVY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 0, 128);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: oldlace;"></div>
pub const OLDLACE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(253, 245, 230);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: olive;"></div>
pub const OLIVE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(128, 128, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: olivedrab;"></div>
pub const OLIVEDRAB: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(107, 142, 35);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: orange;"></div>
pub const ORANGE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 165, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: orangered;"></div>
pub const ORANGERED: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 69, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: orchid;"></div>
pub const ORCHID: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(218, 112, 214);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: palegoldenrod;"></div>
pub const PALEGOLDENROD: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(238, 232, 170);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: palegreen;"></div>
pub const PALEGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(152, 251, 152);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: paleturquoise;"></div>
pub const PALETURQUOISE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(175, 238, 238);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: palevioletred;"></div>
pub const PALEVIOLETRED: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(219, 112, 147);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: papayawhip;"></div>
pub const PAPAYAWHIP: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 239, 213);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: peachpuff;"></div>
pub const PEACHPUFF: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 218, 185);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: peru;"></div>
pub const PERU: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(205, 133, 63);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: pink;"></div>
pub const PINK: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 192, 203);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: plum;"></div>
pub const PLUM: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(221, 160, 221);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: powderblue;"></div>
pub const POWDERBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(176, 224, 230);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: purple;"></div>
pub const PURPLE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(128, 0, 128);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: rebeccapurple;"></div>
pub const REBECCAPURPLE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(102, 51, 153);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: red;"></div>
pub const RED: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 0, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: rosybrown;"></div>
pub const ROSYBROWN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(188, 143, 143);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: royalblue;"></div>
pub const ROYALBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(65, 105, 225);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: saddlebrown;"></div>
pub const SADDLEBROWN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(139, 69, 19);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: salmon;"></div>
pub const SALMON: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(250, 128, 114);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: sandybrown;"></div>
pub const SANDYBROWN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(244, 164, 96);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: seagreen;"></div>
pub const SEAGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(46, 139, 87);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: seashell;"></div>
pub const SEASHELL: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 245, 238);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: sienna;"></div>
pub const SIENNA: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(160, 82, 45);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: silver;"></div>
pub const SILVER: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(192, 192, 192);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: skyblue;"></div>
pub const SKYBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(135, 206, 235);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: slateblue;"></div>
pub const SLATEBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(106, 90, 205);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: slategray;"></div>
pub const SLATEGRAY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(112, 128, 144);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: slategrey;"></div>
pub const SLATEGREY: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(112, 128, 144);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: snow;"></div>
pub const SNOW: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 250, 250);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: springgreen;"></div>
pub const SPRINGGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 255, 127);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: steelblue;"></div>
pub const STEELBLUE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(70, 130, 180);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: tan;"></div>
pub const TAN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(210, 180, 140);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: teal;"></div>
pub const TEAL: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(0, 128, 128);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: thistle;"></div>
pub const THISTLE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(216, 191, 216);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: tomato;"></div>
pub const TOMATO: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 99, 71);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: turquoise;"></div>
pub const TURQUOISE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(64, 224, 208);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: violet;"></div>
pub const VIOLET: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(238, 130, 238);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: wheat;"></div>
pub const WHEAT: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(245, 222, 179);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: white;"></div>
pub const WHITE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 255, 255);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: whitesmoke;"></div>
pub const WHITESMOKE: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(245, 245, 245);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: yellow;"></div>
pub const YELLOW: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(255, 255, 0);

///<div style="display: inline-block; width: 3em; height: 1em; border: 1px solid black; background: yellowgreen;"></div>
pub const YELLOWGREEN: crate::rgb::Srgb<u8> = crate::rgb::Srgb::new(154, 205, 50);
static COLORS: ::phf::Map<&'static str, crate::rgb::Srgb<u8>> = phf::phf_map! {
    "aliceblue" => ALICEBLUE,
    "antiquewhite" => ANTIQUEWHITE,
    "aqua" => AQUA,
    "aquamarine" => AQUAMARINE,
    "azure" => AZURE,
    "beige" => BEIGE,
    "bisque" => BISQUE,
    "black" => BLACK,
    "blanchedalmond" => BLANCHEDALMOND,
    "blue" => BLUE,
    "blueviolet" => BLUEVIOLET,
    "brown" => BROWN,
    "burlywood" => BURLYWOOD,
    "cadetblue" => CADETBLUE,
    "chartreuse" => CHARTREUSE,
    "chocolate" => CHOCOLATE,
    "coral" => CORAL,
    "cornflowerblue" => CORNFLOWERBLUE,
    "cornsilk" => CORNSILK,
    "crimson" => CRIMSON,
    "cyan" => CYAN,
    "darkblue" => DARKBLUE,
    "darkcyan" => DARKCYAN,
    "darkgoldenrod" => DARKGOLDENROD,
    "darkgray" => DARKGRAY,
    "darkgreen" => DARKGREEN,
    "darkgrey" => DARKGREY,
    "darkkhaki" => DARKKHAKI,
    "darkmagenta" => DARKMAGENTA,
    "darkolivegreen" => DARKOLIVEGREEN,
    "darkorange" => DARKORANGE,
    "darkorchid" => DARKORCHID,
    "darkred" => DARKRED,
    "darksalmon" => DARKSALMON,
    "darkseagreen" => DARKSEAGREEN,
    "darkslateblue" => DARKSLATEBLUE,
    "darkslategray" => DARKSLATEGRAY,
    "darkslategrey" => DARKSLATEGREY,
    "darkturquoise" => DARKTURQUOISE,
    "darkviolet" => DARKVIOLET,
    "deeppink" => DEEPPINK,
    "deepskyblue" => DEEPSKYBLUE,
    "dimgray" => DIMGRAY,
    "dimgrey" => DIMGREY,
    "dodgerblue" => DODGERBLUE,
    "firebrick" => FIREBRICK,
    "floralwhite" => FLORALWHITE,
    "forestgreen" => FORESTGREEN,
    "fuchsia" => FUCHSIA,
    "gainsboro" => GAINSBORO,
    "ghostwhite" => GHOSTWHITE,
    "gold" => GOLD,
    "goldenrod" => GOLDENROD,
    "gray" => GRAY,
    "grey" => GREY,
    "green" => GREEN,
    "greenyellow" => GREENYELLOW,
    "honeydew" => HONEYDEW,
    "hotpink" => HOTPINK,
    "indianred" => INDIANRED,
    "indigo" => INDIGO,
    "ivory" => IVORY,
    "khaki" => KHAKI,
    "lavender" => LAVENDER,
    "lavenderblush" => LAVENDERBLUSH,
    "lawngreen" => LAWNGREEN,
    "lemonchiffon" => LEMONCHIFFON,
    "lightblue" => LIGHTBLUE,
    "lightcoral" => LIGHTCORAL,
    "lightcyan" => LIGHTCYAN,
    "lightgoldenrodyellow" => LIGHTGOLDENRODYELLOW,
    "lightgray" => LIGHTGRAY,
    "lightgreen" => LIGHTGREEN,
    "lightgrey" => LIGHTGREY,
    "lightpink" => LIGHTPINK,
    "lightsalmon" => LIGHTSALMON,
    "lightseagreen" => LIGHTSEAGREEN,
    "lightskyblue" => LIGHTSKYBLUE,
    "lightslategray" => LIGHTSLATEGRAY,
    "lightslategrey" => LIGHTSLATEGREY,
    "lightsteelblue" => LIGHTSTEELBLUE,
    "lightyellow" => LIGHTYELLOW,
    "lime" => LIME,
    "limegreen" => LIMEGREEN,
    "linen" => LINEN,
    "magenta" => MAGENTA,
    "maroon" => MAROON,
    "mediumaquamarine" => MEDIUMAQUAMARINE,
    "mediumblue" => MEDIUMBLUE,
    "mediumorchid" => MEDIUMORCHID,
    "mediumpurple" => MEDIUMPURPLE,
    "mediumseagreen" => MEDIUMSEAGREEN,
    "mediumslateblue" => MEDIUMSLATEBLUE,
    "mediumspringgreen" => MEDIUMSPRINGGREEN,
    "mediumturquoise" => MEDIUMTURQUOISE,
    "mediumvioletred" => MEDIUMVIOLETRED,
    "midnightblue" => MIDNIGHTBLUE,
    "mintcream" => MINTCREAM,
    "mistyrose" => MISTYROSE,
    "moccasin" => MOCCASIN,
    "navajowhite" => NAVAJOWHITE,
    "navy" => NAVY,
    "oldlace" => OLDLACE,
    "olive" => OLIVE,
    "olivedrab" => OLIVEDRAB,
    "orange" => ORANGE,
    "orangered" => ORANGERED,
    "orchid" => ORCHID,
    "palegoldenrod" => PALEGOLDENROD,
    "palegreen" => PALEGREEN,
    "paleturquoise" => PALETURQUOISE,
    "palevioletred" => PALEVIOLETRED,
    "papayawhip" => PAPAYAWHIP,
    "peachpuff" => PEACHPUFF,
    "peru" => PERU,
    "pink" => PINK,
    "plum" => PLUM,
    "powderblue" => POWDERBLUE,
    "purple" => PURPLE,
    "rebeccapurple" => REBECCAPURPLE,
    "red" => RED,
    "rosybrown" => ROSYBROWN,
    "royalblue" => ROYALBLUE,
    "saddlebrown" => SADDLEBROWN,
    "salmon" => SALMON,
    "sandybrown" => SANDYBROWN,
    "seagreen" => SEAGREEN,
    "seashell" => SEASHELL,
    "sienna" => SIENNA,
    "silver" => SILVER,
    "skyblue" => SKYBLUE,
    "slateblue" => SLATEBLUE,
    "slategray" => SLATEGRAY,
    "slategrey" => SLATEGREY,
    "snow" => SNOW,
    "springgreen" => SPRINGGREEN,
    "steelblue" => STEELBLUE,
    "tan" => TAN,
    "teal" => TEAL,
    "thistle" => THISTLE,
    "tomato" => TOMATO,
    "turquoise" => TURQUOISE,
    "violet" => VIOLET,
    "wheat" => WHEAT,
    "white" => WHITE,
    "whitesmoke" => WHITESMOKE,
    "yellow" => YELLOW,
    "yellowgreen" => YELLOWGREEN,
};
