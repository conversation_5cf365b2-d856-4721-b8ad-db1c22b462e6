import 'package:flutter/material.dart';

/// Collage Screen
/// 
/// Provides interface for creating and editing collages from multiple images.
/// Includes layout selection, image arrangement, and styling options.
class CollageScreen extends StatelessWidget {
  final List<String> imageIds;

  const CollageScreen({
    super.key,
    required this.imageIds,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Collage'),
        actions: [
          TextButton(
            onPressed: () {
              // TODO: Save collage
            },
            child: const Text('Save'),
          ),
        ],
      ),
      body: Center(
        child: Text('Collage Screen - Images: ${imageIds.length}'),
      ),
    );
  }
}
