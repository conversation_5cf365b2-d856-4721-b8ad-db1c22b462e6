import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'controllers/collage_editor_controller.dart';
import 'widgets/layout_selector.dart';
import 'widgets/collage_canvas.dart';
import 'widgets/style_editor.dart';
import 'models/collage_models.dart';
import '../../core/services/service_locator.dart';
import '../../core/services/image_service.dart';

/// Collage Screen
///
/// Provides interface for creating and editing collages from multiple images.
/// Includes layout selection, image arrangement, and styling options.
class CollageScreen extends StatefulWidget {
  final List<String> imageIds;

  const CollageScreen({super.key, required this.imageIds});

  @override
  State<CollageScreen> createState() => _CollageScreenState();
}

class _CollageScreenState extends State<CollageScreen> {
  late CollageEditorController _controller;
  bool _isStylePanelOpen = false;

  @override
  void initState() {
    super.initState();
    _controller = CollageEditorController(getIt<ImageService>());
    _controller.addListener(_onControllerChanged);
    _controller.initialize(widget.imageIds);
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onControllerChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: _buildAppBar(context),
      body: _buildBody(context),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    if (_controller.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_controller.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _controller.error!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('返回'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Layout selector (always visible)
        if (_controller.selectedLayout == null)
          Expanded(child: _buildLayoutSelection(context))
        else ...[
          // Selected layout info
          LayoutInfo(layout: _controller.selectedLayout!),

          // Main content area
          Expanded(child: _buildMainContent(context)),

          // Bottom controls
          _buildBottomControls(context),
        ],
      ],
    );
  }

  Widget? _buildFloatingActionButton(BuildContext context) {
    if (_controller.selectedLayout == null) {
      return null;
    }

    return FloatingActionButton(
      onPressed: () {
        setState(() {
          _isStylePanelOpen = !_isStylePanelOpen;
        });
      },
      child: Icon(_isStylePanelOpen ? Icons.close : Icons.palette),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text('创建拼图'),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => context.pop(),
      ),
      actions: [
        TextButton(
          onPressed: _controller.canSave ? () => _saveCollage(context) : null,
          child: Text(
            '保存',
            style: TextStyle(
              color: _controller.canSave
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.4),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLayoutSelection(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            '选择拼图布局',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600),
          ),
        ),
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: CollageLayoutConfig.availableLayouts.length,
            itemBuilder: (context, index) {
              final layout = CollageLayoutConfig.availableLayouts[index];
              return _LayoutCard(
                layout: layout,
                imageCount: _controller.imagePaths.length,
                onTap: () => _controller.selectLayout(layout),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMainContent(BuildContext context) {
    return Row(
      children: [
        // Canvas area
        Expanded(
          flex: _isStylePanelOpen ? 2 : 3,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: CollageCanvas(
              items: _controller.collageItems,
              style: _controller.style,
              selectedItemIndex: _controller.selectedItemIndex,
              isPreviewMode: _controller.isPreviewMode,
              onItemSelected: _controller.selectItem,
              onItemMoved: _controller.updateItemPosition,
              onItemScaled: _controller.updateItemScale,
            ),
          ),
        ),

        // Style panel
        if (_isStylePanelOpen)
          Expanded(
            flex: 1,
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  left: BorderSide(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
              ),
              child: StyleEditor(
                style: _controller.style,
                onStyleChanged: _controller.updateStyle,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBottomControls(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _ControlButton(
            icon: _controller.isPreviewMode ? Icons.edit : Icons.preview,
            label: _controller.isPreviewMode ? '编辑' : '预览',
            onPressed: _controller.togglePreviewMode,
          ),
          _ControlButton(
            icon: Icons.add_photo_alternate,
            label: '添加图片',
            onPressed: _controller.addMoreImages,
          ),
          _ControlButton(
            icon: Icons.refresh,
            label: '重新选择',
            onPressed: () {
              _controller.reset();
            },
          ),
        ],
      ),
    );
  }

  Future<void> _saveCollage(BuildContext context) async {
    final result = await _controller.saveCollage();

    if (result != null && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('拼图保存成功！'), backgroundColor: Colors.green),
      );
      Navigator.of(context).pop();
    } else if (context.mounted && _controller.error != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_controller.error!),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

class _LayoutCard extends StatelessWidget {
  final CollageLayoutConfig layout;
  final int imageCount;
  final VoidCallback onTap;

  const _LayoutCard({
    required this.layout,
    required this.imageCount,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final canUse = layout.maxImages >= imageCount;

    return GestureDetector(
      onTap: canUse ? onTap : null,
      child: Container(
        decoration: BoxDecoration(
          color: canUse
              ? colorScheme.surface
              : colorScheme.surface.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: colorScheme.outline.withValues(alpha: 0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              layout.icon,
              size: 32,
              color: canUse
                  ? colorScheme.primary
                  : colorScheme.onSurface.withValues(alpha: 0.4),
            ),
            const SizedBox(height: 8),
            Text(
              layout.name,
              style: theme.textTheme.titleSmall?.copyWith(
                color: canUse
                    ? colorScheme.onSurface
                    : colorScheme.onSurface.withValues(alpha: 0.4),
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              layout.description,
              style: theme.textTheme.bodySmall?.copyWith(
                color: canUse
                    ? colorScheme.onSurface.withValues(alpha: 0.7)
                    : colorScheme.onSurface.withValues(alpha: 0.4),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: canUse
                    ? colorScheme.primary.withValues(alpha: 0.1)
                    : colorScheme.onSurface.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '最多 ${layout.maxImages} 张',
                style: theme.textTheme.labelSmall?.copyWith(
                  color: canUse
                      ? colorScheme.primary
                      : colorScheme.onSurface.withValues(alpha: 0.4),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ControlButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback? onPressed;

  const _ControlButton({
    required this.icon,
    required this.label,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: onPressed != null
                  ? colorScheme.primary
                  : colorScheme.onSurface.withValues(alpha: 0.4),
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: theme.textTheme.labelSmall?.copyWith(
                color: onPressed != null
                    ? colorScheme.primary
                    : colorScheme.onSurface.withValues(alpha: 0.4),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
