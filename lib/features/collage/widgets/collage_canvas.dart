import 'dart:io';
import 'package:flutter/material.dart';
import '../models/collage_models.dart';

/// Collage canvas widget for editing and previewing collages
class CollageCanvas extends StatefulWidget {
  final List<CollageImageItem> items;
  final CollageStyle style;
  final int? selectedItemIndex;
  final bool isPreviewMode;
  final ValueChanged<int>? onItemSelected;
  final Function(int, Offset)? onItemMoved;
  final Function(int, double)? onItemScaled;
  final Function(int, int)? onItemsSwapped;

  const CollageCanvas({
    super.key,
    required this.items,
    required this.style,
    this.selectedItemIndex,
    this.isPreviewMode = false,
    this.onItemSelected,
    this.onItemMoved,
    this.onItemScaled,
    this.onItemsSwapped,
  });

  @override
  State<CollageCanvas> createState() => _CollageCanvasState();
}

class _CollageCanvasState extends State<CollageCanvas> {
  late TransformationController _transformationController;
  
  @override
  void initState() {
    super.initState();
    _transformationController = TransformationController();
  }

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.style.backgroundColor,
        borderRadius: BorderRadius.circular(widget.style.cornerRadius),
        boxShadow: widget.style.hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(widget.style.cornerRadius),
        child: AspectRatio(
          aspectRatio: 1.0, // Square canvas for now
          child: InteractiveViewer(
            transformationController: _transformationController,
            minScale: 0.5,
            maxScale: 3.0,
            constrained: false,
            child: Container(
              width: 400,
              height: 400,
              child: Stack(
                children: [
                  // Background
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: widget.style.backgroundColor,
                  ),
                  
                  // Collage items
                  ...widget.items.asMap().entries.map((entry) {
                    final index = entry.key;
                    final item = entry.value;
                    return _CollageImageWidget(
                      key: ValueKey('${item.imagePath}_$index'),
                      item: item,
                      index: index,
                      isSelected: widget.selectedItemIndex == index,
                      isPreviewMode: widget.isPreviewMode,
                      style: widget.style,
                      onTap: () => widget.onItemSelected?.call(index),
                      onPanUpdate: (delta) => _handleItemMove(index, delta),
                      onScaleUpdate: (scale) => widget.onItemScaled?.call(index, scale),
                    );
                  }).toList(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleItemMove(int index, Offset delta) {
    if (widget.onItemMoved != null) {
      final currentItem = widget.items[index];
      final newOffset = currentItem.offset + delta;
      widget.onItemMoved!(index, newOffset);
    }
  }
}

class _CollageImageWidget extends StatefulWidget {
  final CollageImageItem item;
  final int index;
  final bool isSelected;
  final bool isPreviewMode;
  final CollageStyle style;
  final VoidCallback? onTap;
  final ValueChanged<Offset>? onPanUpdate;
  final ValueChanged<double>? onScaleUpdate;

  const _CollageImageWidget({
    super.key,
    required this.item,
    required this.index,
    required this.isSelected,
    required this.isPreviewMode,
    required this.style,
    this.onTap,
    this.onPanUpdate,
    this.onScaleUpdate,
  });

  @override
  State<_CollageImageWidget> createState() => _CollageImageWidgetState();
}

class _CollageImageWidgetState extends State<_CollageImageWidget> {
  double _currentScale = 1.0;

  @override
  void initState() {
    super.initState();
    _currentScale = widget.item.scale;
  }

  @override
  Widget build(BuildContext context) {
    final region = widget.item.region;
    final totalOffset = widget.item.offset;

    return Positioned(
      left: region.x * 4 + totalOffset.dx, // Scale to canvas size
      top: region.y * 4 + totalOffset.dy,
      width: region.width * 4,
      height: region.height * 4,
      child: Transform.rotate(
        angle: region.rotation * 3.14159 / 180, // Convert to radians
        child: GestureDetector(
          onTap: widget.isPreviewMode ? null : widget.onTap,
          onPanUpdate: widget.isPreviewMode 
              ? null 
              : (details) => widget.onPanUpdate?.call(details.delta),
          onScaleUpdate: widget.isPreviewMode
              ? null
              : (details) {
                  setState(() {
                    _currentScale = (widget.item.scale * details.scale).clamp(0.5, 3.0);
                  });
                },
          onScaleEnd: widget.isPreviewMode
              ? null
              : (details) {
                  widget.onScaleUpdate?.call(_currentScale);
                },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(region.borderRadius),
              border: widget.isSelected && !widget.isPreviewMode
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2,
                    )
                  : widget.style.borderWidth > 0
                      ? Border.all(
                          color: widget.style.borderColor,
                          width: widget.style.borderWidth,
                        )
                      : null,
              boxShadow: widget.style.hasShadow
                  ? [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(region.borderRadius),
              child: Transform.scale(
                scale: _currentScale,
                child: _buildImage(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImage() {
    return Image.file(
      File(widget.item.imagePath),
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          color: Colors.grey[300],
          child: const Icon(
            Icons.broken_image,
            color: Colors.grey,
            size: 32,
          ),
        );
      },
    );
  }
}

/// Collage canvas controls
class CollageCanvasControls extends StatelessWidget {
  final bool isPreviewMode;
  final VoidCallback? onPreviewToggle;
  final VoidCallback? onReset;
  final VoidCallback? onUndo;
  final VoidCallback? onRedo;

  const CollageCanvasControls({
    super.key,
    required this.isPreviewMode,
    this.onPreviewToggle,
    this.onReset,
    this.onUndo,
    this.onRedo,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _ControlButton(
            icon: isPreviewMode ? Icons.edit : Icons.preview,
            label: isPreviewMode ? '编辑' : '预览',
            onPressed: onPreviewToggle,
          ),
          _ControlButton(
            icon: Icons.undo,
            label: '撤销',
            onPressed: onUndo,
          ),
          _ControlButton(
            icon: Icons.redo,
            label: '重做',
            onPressed: onRedo,
          ),
          _ControlButton(
            icon: Icons.refresh,
            label: '重置',
            onPressed: onReset,
          ),
        ],
      ),
    );
  }
}

class _ControlButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback? onPressed;

  const _ControlButton({
    required this.icon,
    required this.label,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: onPressed != null 
                  ? colorScheme.primary 
                  : colorScheme.onSurface.withOpacity(0.4),
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: theme.textTheme.labelSmall?.copyWith(
                color: onPressed != null 
                    ? colorScheme.primary 
                    : colorScheme.onSurface.withOpacity(0.4),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
