import 'package:flutter/material.dart';
import '../../core/widgets/widgets.dart';
import '../../app/router.dart';

/// Home Screen
///
/// Main landing screen of the TuShen app. Provides quick access to
/// core features like creating collages, editing images, and browsing gallery.
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('TuShen'),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Implement notifications
            },
          ),
        ],
      ),
      body: const SingleChildScrollView(
        padding: TuShenPadding.allMd,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _WelcomeSection(),
            TuShenGaps.verticalXl,
            _QuickActionsSection(),
            TuShenGaps.verticalXl,
            _RecentWorkSection(),
            TuShenGaps.verticalXl,
            _FeaturesSection(),
          ],
        ),
      ),
    );
  }
}

class _WelcomeSection extends StatelessWidget {
  const _WelcomeSection();

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: TuShenPadding.allLg,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: TuShenColors.primaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: TuShenRadius.lg,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome to TuShen',
            style: TuShenTypography.headlineSmall.copyWith(
              color: Colors.white,
              fontWeight: TuShenTypography.bold,
            ),
          ),
          TuShenGaps.verticalSm,
          Text(
            'Create stunning collages and edit your photos with professional tools',
            style: TuShenTypography.bodyMedium.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }
}

class _QuickActionsSection extends StatelessWidget {
  const _QuickActionsSection();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: TuShenTypography.titleLarge.copyWith(
            fontWeight: TuShenTypography.bold,
          ),
        ),
        TuShenGaps.verticalMd,
        Row(
          children: [
            Expanded(
              child: _QuickActionCard(
                icon: Icons.photo_library,
                title: 'Create Collage',
                subtitle: 'Combine multiple photos',
                onTap: () {
                  context.goCollage([]);
                },
              ),
            ),
            TuShenGaps.horizontalSm,
            Expanded(
              child: _QuickActionCard(
                icon: Icons.edit,
                title: 'Edit Photo',
                subtitle: 'Apply filters & effects',
                onTap: () {
                  context.goEditor('');
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _QuickActionCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _QuickActionCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return TuShenCard(
      onTap: onTap,
      child: Column(
        children: [
          Icon(
            icon,
            size: TuShenSpacing.iconXl,
            color: theme.colorScheme.primary,
          ),
          TuShenGaps.verticalSm,
          Text(
            title,
            style: TuShenTypography.titleMedium.copyWith(
              fontWeight: TuShenTypography.semiBold,
            ),
            textAlign: TextAlign.center,
          ),
          TuShenGaps.verticalXs,
          Text(
            subtitle,
            style: TuShenTypography.bodySmall.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _RecentWorkSection extends StatelessWidget {
  const _RecentWorkSection();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Work',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            TuShenButton(
              variant: TuShenButtonVariant.text,
              size: TuShenButtonSize.small,
              onPressed: () {
                context.goGallery();
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5, // TODO: Replace with actual recent work count
            itemBuilder: (context, index) {
              return Container(
                width: 100,
                margin: const EdgeInsets.only(right: 12),
                child: TuShenCard(
                  variant: TuShenCardVariant.outlined,
                  child: Center(
                    child: Icon(
                      Icons.image,
                      size: 40,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class _FeaturesSection extends StatelessWidget {
  const _FeaturesSection();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Features',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        const _FeatureItem(
          icon: Icons.auto_awesome,
          title: 'AI-Powered Filters',
          description: 'Intelligent filters that adapt to your photos',
        ),
        const _FeatureItem(
          icon: Icons.grid_view,
          title: 'Smart Collages',
          description: 'Automatically arrange photos in beautiful layouts',
        ),
        const _FeatureItem(
          icon: Icons.high_quality,
          title: 'High Quality Output',
          description: 'Professional-grade image processing',
        ),
        const _FeatureItem(
          icon: Icons.devices,
          title: 'Cross-Platform',
          description: 'Works seamlessly across all your devices',
        ),
      ],
    );
  }
}

class _FeatureItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;

  const _FeatureItem({
    required this.icon,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: Theme.of(context).colorScheme.primary),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
