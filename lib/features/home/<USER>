import 'package:flutter/material.dart';
import '../../core/widgets/widgets.dart';
import '../../core/widgets/dialogs/image_picker_bottom_sheet.dart';
import '../../core/controllers/image_controller.dart';
import '../../core/services/service_locator.dart';
import '../../app/routes.dart';
import '../../l10n/generated/app_localizations.dart';

/// Home Screen
///
/// Main landing screen of the TuShen app. Provides quick access to
/// core features like creating collages, editing images, and browsing gallery.
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late final ImageController _imageController;

  @override
  void initState() {
    super.initState();
    _imageController = getIt<ImageController>();
    // Load recent images when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _imageController.loadImages();
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.appTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Implement notifications
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: TuShenPadding.allMd,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _WelcomeSection(),
            TuShenGaps.verticalXl,
            _QuickActionsSection(imageController: _imageController),
            TuShenGaps.verticalXl,
            _RecentWorkSection(imageController: _imageController),
            TuShenGaps.verticalXl,
            _FeaturesSection(),
          ],
        ),
      ),
    );
  }
}

class _WelcomeSection extends StatelessWidget {
  const _WelcomeSection();

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Container(
      width: double.infinity,
      padding: TuShenPadding.allLg,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: TuShenColors.primaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: TuShenRadius.lg,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.welcome,
            style: TuShenTypography.headlineSmall.copyWith(
              color: Colors.white,
              fontWeight: TuShenTypography.bold,
            ),
          ),
          TuShenGaps.verticalSm,
          Text(
            l10n.welcomeSubtitle,
            style: TuShenTypography.bodyMedium.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }
}

class _QuickActionsSection extends StatelessWidget {
  final ImageController imageController;

  const _QuickActionsSection({required this.imageController});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.quickActions,
          style: TuShenTypography.titleLarge.copyWith(
            fontWeight: TuShenTypography.bold,
          ),
        ),
        TuShenGaps.verticalMd,
        Row(
          children: [
            Expanded(
              child: _QuickActionCard(
                icon: Icons.photo_library,
                title: l10n.createCollage,
                subtitle: l10n.createCollageSubtitle,
                onTap: () => _handleCollageAction(context),
              ),
            ),
            TuShenGaps.horizontalSm,
            Expanded(
              child: _QuickActionCard(
                icon: Icons.edit,
                title: l10n.editPhoto,
                subtitle: l10n.editPhotoSubtitle,
                onTap: () => _handleEditAction(context),
              ),
            ),
          ],
        ),
        TuShenGaps.verticalMd,
        Row(
          children: [
            Expanded(
              child: _QuickActionCard(
                icon: Icons.play_circle_outline,
                title: 'Live图生成',
                subtitle: '将图片转换为动态Live图',
                onTap: () => _handleLivePhotoAction(context),
              ),
            ),
            TuShenGaps.horizontalSm,
            Expanded(
              child: _QuickActionCard(
                icon: Icons.transform,
                title: '格式转换',
                subtitle: '转换图片格式和压缩',
                onTap: () => _handleFormatConvertAction(context),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _handleCollageAction(BuildContext context) {
    ImagePickerBottomSheet.show(
      context,
      onMultiplePressed: () async {
        await imageController.pickMultipleImages();
        if (context.mounted && imageController.images.isNotEmpty) {
          context.pushCollage(
            imageController.images.map((img) => img.path).toList(),
          );
        }
      },
      onGalleryPressed: () async {
        await imageController.pickImageFromGallery();
        if (context.mounted && imageController.currentImage != null) {
          context.pushCollage([imageController.currentImage!.path]);
        }
      },
      onCameraPressed: () async {
        await imageController.pickImageFromCamera();
        if (context.mounted && imageController.currentImage != null) {
          context.pushCollage([imageController.currentImage!.path]);
        }
      },
    );
  }

  void _handleEditAction(BuildContext context) {
    ImagePickerBottomSheet.show(
      context,
      showMultipleOption: false,
      onGalleryPressed: () async {
        await imageController.pickImageFromGallery();
        if (context.mounted && imageController.currentImage != null) {
          context.pushEditor(imageController.currentImage!.path);
        }
      },
      onCameraPressed: () async {
        await imageController.pickImageFromCamera();
        if (context.mounted && imageController.currentImage != null) {
          context.pushEditor(imageController.currentImage!.path);
        }
      },
    );
  }

  void _handleLivePhotoAction(BuildContext context) {
    ImagePickerBottomSheet.show(
      context,
      showMultipleOption: false,
      onGalleryPressed: () async {
        await imageController.pickImageFromGallery();
        if (context.mounted && imageController.currentImage != null) {
          context.pushLivePhoto(imageId: imageController.currentImage!.path);
        }
      },
      onCameraPressed: () async {
        await imageController.pickImageFromCamera();
        if (context.mounted && imageController.currentImage != null) {
          context.pushLivePhoto(imageId: imageController.currentImage!.path);
        }
      },
    );
  }

  void _handleFormatConvertAction(BuildContext context) {
    ImagePickerBottomSheet.show(
      context,
      showMultipleOption: false,
      onGalleryPressed: () async {
        await imageController.pickImageFromGallery();
        if (context.mounted && imageController.currentImage != null) {
          context.pushFormatConverter(
            imageId: imageController.currentImage!.path,
          );
        }
      },
      onCameraPressed: () async {
        await imageController.pickImageFromCamera();
        if (context.mounted && imageController.currentImage != null) {
          context.pushFormatConverter(
            imageId: imageController.currentImage!.path,
          );
        }
      },
    );
  }
}

class _QuickActionCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _QuickActionCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return TuShenCard(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: TuShenColors.primaryGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: TuShenColors.primary.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(icon, size: TuShenSpacing.iconXl, color: Colors.white),
          ),
          TuShenGaps.verticalSm,
          Text(
            title,
            style: TuShenTypography.titleMedium.copyWith(
              fontWeight: TuShenTypography.semiBold,
            ),
            textAlign: TextAlign.center,
          ),
          TuShenGaps.verticalXs,
          Text(
            subtitle,
            style: TuShenTypography.bodySmall.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _RecentWorkSection extends StatelessWidget {
  final ImageController imageController;

  const _RecentWorkSection({required this.imageController});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Work',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            TuShenButton(
              variant: TuShenButtonVariant.text,
              size: TuShenButtonSize.small,
              onPressed: () {
                // 切换到Gallery标签页
                Navigator.of(context).pushNamed(
                  AppRoutes.main,
                  arguments: MainShellArguments(initialIndex: 1),
                );
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListenableBuilder(
            listenable: imageController,
            builder: (context, child) {
              final images = imageController.images;

              if (imageController.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (images.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.photo_library_outlined,
                        size: 48,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'No recent images',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: images.length > 10 ? 10 : images.length,
                itemBuilder: (context, index) {
                  final imageData = images[index];
                  return Container(
                    width: 100,
                    margin: const EdgeInsets.only(right: 12),
                    child: TuShenCard(
                      variant: TuShenCardVariant.outlined,
                      onTap: () {
                        context.pushEditor(imageData.path);
                      },
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.memory(
                          imageData.bytes,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            return Center(
                              child: Icon(
                                Icons.broken_image,
                                size: 40,
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurfaceVariant,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }
}

class _FeaturesSection extends StatelessWidget {
  const _FeaturesSection();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Features',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        const _FeatureItem(
          icon: Icons.auto_awesome,
          title: 'AI-Powered Filters',
          description: 'Intelligent filters that adapt to your photos',
        ),
        const _FeatureItem(
          icon: Icons.grid_view,
          title: 'Smart Collages',
          description: 'Automatically arrange photos in beautiful layouts',
        ),
        const _FeatureItem(
          icon: Icons.high_quality,
          title: 'High Quality Output',
          description: 'Professional-grade image processing',
        ),
        const _FeatureItem(
          icon: Icons.devices,
          title: 'Cross-Platform',
          description: 'Works seamlessly across all your devices',
        ),
      ],
    );
  }
}

class _FeatureItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;

  const _FeatureItem({
    required this.icon,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: Theme.of(context).colorScheme.primary),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
