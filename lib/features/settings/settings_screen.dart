import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'controllers/settings_controller.dart';
import 'models/settings_models.dart';
import 'widgets/settings_section.dart';
import 'widgets/settings_tile.dart';
import 'widgets/settings_switch_tile.dart';
import 'widgets/theme_selector_dialog.dart';
import 'widgets/language_selector_dialog.dart';
import 'widgets/quality_selector_dialog.dart';
import 'widgets/about_dialog.dart';
import '../../app/router.dart';
import '../../core/design_system/colors.dart';

/// Settings Screen
///
/// Provides access to app settings, preferences, and configuration options.
/// Includes theme, language, quality settings, and more.
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late final SettingsController _controller;

  @override
  void initState() {
    super.initState();
    _controller = SettingsController();
    _controller.initialize();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return Scaffold(
          backgroundColor: Theme.of(context).colorScheme.surface,
          body: CustomScrollView(
            slivers: [_buildAppBar(context), _buildContent(context)],
          ),
        );
      },
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        title: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: TuShenColors.primaryGradient,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: const Text(
            'Settings',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        titlePadding: const EdgeInsets.only(left: 16, bottom: 16),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: TuShenColors.primaryGradient,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (_controller.isLoading) {
      return const SliverFillRemaining(
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (_controller.error != null) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                _controller.error!,
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _controller.loadSettings,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return SliverPadding(
      padding: const EdgeInsets.all(16),
      sliver: SliverList(
        delegate: SliverChildListDelegate([
          _buildAppearanceSection(context),
          const SizedBox(height: 24),
          _buildProcessingSection(context),
          const SizedBox(height: 24),
          _buildBehaviorSection(context),
          const SizedBox(height: 24),
          _buildStorageSection(context),
          const SizedBox(height: 24),
          if (kDebugMode) ...[
            _buildDebugSection(context),
            const SizedBox(height: 24),
          ],
          _buildAboutSection(context),
          const SizedBox(height: 32),
        ]),
      ),
    );
  }

  Widget _buildAppearanceSection(BuildContext context) {
    return SettingsSection(
      title: 'Appearance',
      children: [
        SettingsTile(
          icon: _controller.settings.themeMode.icon,
          title: 'Theme',
          subtitle: _controller.settings.themeMode.displayName,
          onTap: () => _showThemeSelector(context),
        ),
        SettingsTile(
          icon: Icons.language,
          title: 'Language',
          subtitle: _controller.settings.language.displayName,
          onTap: () => _showLanguageSelector(context),
        ),
      ],
    );
  }

  Widget _buildProcessingSection(BuildContext context) {
    return SettingsSection(
      title: 'Processing',
      children: [
        SettingsTile(
          icon: Icons.high_quality,
          title: 'Image Quality',
          subtitle: _controller.settings.imageQuality.displayName,
          onTap: () => _showQualitySelector(context),
        ),
        SettingsTile(
          icon: Icons.speed,
          title: 'Processing Speed',
          subtitle: _controller.settings.processingSpeed.displayName,
          onTap: () => _showProcessingSpeedSelector(context),
        ),
        SettingsTile(
          icon: Icons.memory,
          title: 'Memory Usage',
          subtitle: _controller.settings.memoryUsage.displayName,
          onTap: () => _showMemoryUsageSelector(context),
        ),
      ],
    );
  }

  Widget _buildBehaviorSection(BuildContext context) {
    return SettingsSection(
      title: 'Behavior',
      children: [
        SettingsSwitchTile(
          icon: Icons.vibration,
          title: 'Haptic Feedback',
          subtitle: 'Feel vibrations for interactions',
          value: _controller.settings.enableHapticFeedback,
          onChanged: _controller.updateHapticFeedback,
        ),
        SettingsSwitchTile(
          icon: Icons.volume_up,
          title: 'Sound Effects',
          subtitle: 'Play sounds for actions',
          value: _controller.settings.enableSoundEffects,
          onChanged: _controller.updateSoundEffects,
        ),
        SettingsSwitchTile(
          icon: Icons.save,
          title: 'Auto Save',
          subtitle: 'Automatically save your work',
          value: _controller.settings.enableAutoSave,
          onChanged: _controller.updateAutoSave,
        ),
        SettingsSwitchTile(
          icon: Icons.cloud_sync,
          title: 'Cloud Sync',
          subtitle: 'Sync data across devices',
          value: _controller.settings.enableCloudSync,
          onChanged: _controller.updateCloudSync,
        ),
      ],
    );
  }

  Widget _buildStorageSection(BuildContext context) {
    return SettingsSection(
      title: 'Storage',
      children: [
        SettingsTile(
          icon: Icons.folder,
          title: 'Cache Size',
          subtitle: 'Currently using 125.5 MB',
          onTap: () => _showCacheManagement(context),
        ),
        SettingsTile(
          icon: Icons.history,
          title: 'Recent Items',
          subtitle: 'Keep ${_controller.settings.maxRecentItems} items',
          onTap: () => _showRecentItemsSelector(context),
        ),
        SettingsSwitchTile(
          icon: Icons.analytics,
          title: 'Analytics',
          subtitle: 'Help improve the app',
          value: _controller.settings.enableAnalytics,
          onChanged: _controller.updateAnalytics,
        ),
      ],
    );
  }

  Widget _buildDebugSection(BuildContext context) {
    return SettingsSection(
      title: 'Debug',
      children: [
        SettingsTile(
          icon: Icons.bug_report,
          title: 'FFI Test',
          subtitle: 'Test Rust backend integration',
          onTap: () => context.goFFITest(),
        ),
        SettingsTile(
          icon: Icons.refresh,
          title: 'Reset Settings',
          subtitle: 'Reset all settings to defaults',
          onTap: () => _showResetConfirmation(context),
        ),
      ],
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return SettingsSection(
      title: 'About',
      children: [
        SettingsTile(
          icon: Icons.info,
          title: 'About TuShen',
          subtitle: 'Version ${_controller.getAppVersion()}',
          onTap: () => _showAboutDialog(context),
        ),
        SettingsTile(
          icon: Icons.privacy_tip,
          title: 'Privacy Policy',
          subtitle: 'How we protect your data',
          onTap: () => _showPrivacyPolicy(context),
        ),
        SettingsTile(
          icon: Icons.description,
          title: 'Terms of Service',
          subtitle: 'Terms and conditions',
          onTap: () => _showTermsOfService(context),
        ),
      ],
    );
  }

  // Dialog methods
  void _showThemeSelector(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ThemeSelectorDialog(
        currentTheme: _controller.settings.themeMode,
        onThemeSelected: _controller.updateThemeMode,
      ),
    );
  }

  void _showLanguageSelector(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => LanguageSelectorDialog(
        currentLanguage: _controller.settings.language,
        onLanguageSelected: _controller.updateLanguage,
      ),
    );
  }

  void _showQualitySelector(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => QualitySelectorDialog(
        currentQuality: _controller.settings.imageQuality,
        onQualitySelected: _controller.updateImageQuality,
      ),
    );
  }

  void _showProcessingSpeedSelector(BuildContext context) {
    _showOptionSelector<ProcessingSpeed>(
      context: context,
      title: 'Processing Speed',
      options: ProcessingSpeed.values,
      currentValue: _controller.settings.processingSpeed,
      onSelected: _controller.updateProcessingSpeed,
      getDisplayName: (speed) => speed.displayName,
      getDescription: (speed) => speed.description,
    );
  }

  void _showMemoryUsageSelector(BuildContext context) {
    _showOptionSelector<MemoryUsage>(
      context: context,
      title: 'Memory Usage',
      options: MemoryUsage.values,
      currentValue: _controller.settings.memoryUsage,
      onSelected: _controller.updateMemoryUsage,
      getDisplayName: (usage) => usage.displayName,
      getDescription: (usage) => usage.description,
    );
  }

  void _showCacheManagement(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cache Management'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Current cache size: 125.5 MB'),
            const SizedBox(height: 16),
            Text('Clear cache to free up storage space?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _controller.clearCache();
              HapticFeedback.lightImpact();
            },
            child: const Text('Clear Cache'),
          ),
        ],
      ),
    );
  }

  void _showRecentItemsSelector(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Recent Items'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Number of recent items to keep:'),
            const SizedBox(height: 16),
            Slider(
              value: _controller.settings.maxRecentItems.toDouble(),
              min: 10,
              max: 100,
              divisions: 9,
              label: _controller.settings.maxRecentItems.toString(),
              onChanged: (value) {
                _controller.updateMaxRecentItems(value.round());
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _showResetConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text(
          'Are you sure you want to reset all settings to their default values? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _controller.resetToDefaults();
              HapticFeedback.mediumImpact();
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CustomAboutDialog(
        appVersion: _controller.getAppVersion(),
        buildNumber: _controller.getBuildNumber(),
      ),
    );
  }

  void _showPrivacyPolicy(BuildContext context) {
    // TODO: Implement privacy policy viewer
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Privacy Policy coming soon')));
  }

  void _showTermsOfService(BuildContext context) {
    // TODO: Implement terms of service viewer
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Terms of Service coming soon')),
    );
  }

  void _showOptionSelector<T>({
    required BuildContext context,
    required String title,
    required List<T> options,
    required T currentValue,
    required Function(T) onSelected,
    required String Function(T) getDisplayName,
    required String Function(T) getDescription,
  }) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(title, style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 16),
            ...options.map(
              (option) => ListTile(
                title: Text(getDisplayName(option)),
                subtitle: Text(getDescription(option)),
                trailing: currentValue == option
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.primary,
                      )
                    : null,
                onTap: () {
                  onSelected(option);
                  Navigator.pop(context);
                  HapticFeedback.selectionClick();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
