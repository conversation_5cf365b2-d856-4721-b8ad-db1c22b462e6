import 'package:flutter/material.dart';
import '../models/editor_models.dart';

/// Filter selector widget for choosing image filters
class FilterSelector extends StatefulWidget {
  final FilterType? selectedFilter;
  final ValueChanged<FilterType?> onFilterSelected;
  final ValueChanged<double>? onIntensityChanged;
  final double intensity;

  const FilterSelector({
    super.key,
    this.selectedFilter,
    required this.onFilterSelected,
    this.onIntensityChanged,
    this.intensity = 1.0,
  });

  @override
  State<FilterSelector> createState() => _FilterSelectorState();
}

class _FilterSelectorState extends State<FilterSelector>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<FilterCategory> _categories = FilterRegistry.getAllCategories();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        children: [
          // Category tabs
          TabBar(
            controller: _tabController,
            isScrollable: true,
            tabAlignment: TabAlignment.start,
            labelColor: colorScheme.primary,
            unselectedLabelColor: colorScheme.onSurface.withValues(alpha: 0.6),
            indicatorColor: colorScheme.primary,
            tabs: _categories.map((category) {
              return Tab(text: FilterRegistry.getCategoryName(category));
            }).toList(),
          ),
          
          // Filter grid
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: _categories.map((category) {
                return _buildFilterGrid(category);
              }).toList(),
            ),
          ),
          
          // Intensity slider (shown when filter is selected)
          if (widget.selectedFilter != null && widget.onIntensityChanged != null)
            _buildIntensitySlider(),
        ],
      ),
    );
  }

  Widget _buildFilterGrid(FilterCategory category) {
    final filters = FilterRegistry.getFiltersByCategory(category);
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: filters.length + 1, // +1 for "None" option
      itemBuilder: (context, index) {
        if (index == 0) {
          return _buildFilterItem(
            name: '原图',
            isSelected: widget.selectedFilter == null,
            onTap: () => widget.onFilterSelected(null),
          );
        }
        
        final filter = filters[index - 1];
        return _buildFilterItem(
          name: filter.name,
          isSelected: widget.selectedFilter == filter.type,
          onTap: () => widget.onFilterSelected(filter.type),
        );
      },
    );
  }

  Widget _buildFilterItem({
    required String name,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          // Filter preview (placeholder for now)
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: isSelected 
                    ? colorScheme.primary.withValues(alpha: 0.1)
                    : colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
                border: isSelected
                    ? Border.all(color: colorScheme.primary, width: 2)
                    : null,
              ),
              child: Center(
                child: Icon(
                  Icons.photo_filter,
                  color: isSelected 
                      ? colorScheme.primary
                      : colorScheme.onSurface.withValues(alpha: 0.6),
                  size: 24,
                ),
              ),
            ),
          ),
          const SizedBox(height: 4),
          
          // Filter name
          Text(
            name,
            style: theme.textTheme.labelSmall?.copyWith(
              color: isSelected 
                  ? colorScheme.primary
                  : colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildIntensitySlider() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        border: Border(
          top: BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.opacity,
            color: colorScheme.onSurface.withValues(alpha: 0.6),
            size: 20,
          ),
          const SizedBox(width: 12),
          
          Expanded(
            child: Slider(
              value: widget.intensity,
              min: 0.0,
              max: 1.0,
              divisions: 20,
              label: '${(widget.intensity * 100).round()}%',
              onChanged: widget.onIntensityChanged,
            ),
          ),
          
          const SizedBox(width: 12),
          SizedBox(
            width: 40,
            child: Text(
              '${(widget.intensity * 100).round()}%',
              style: theme.textTheme.labelSmall?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}

/// Quick filter selector for common filters
class QuickFilterSelector extends StatelessWidget {
  final FilterType? selectedFilter;
  final ValueChanged<FilterType?> onFilterSelected;

  const QuickFilterSelector({
    super.key,
    this.selectedFilter,
    required this.onFilterSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Quick access filters
    final quickFilters = [
      null, // Original
      FilterType.vintage,
      FilterType.blackWhite,
      FilterType.sepia,
      FilterType.blur,
      FilterType.sharpen,
    ];

    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: quickFilters.length,
        itemBuilder: (context, index) {
          final filter = quickFilters[index];
          final filterInfo = filter != null 
              ? FilterRegistry.getFilterInfo(filter)
              : null;
          final name = filterInfo?.name ?? '原图';
          final isSelected = selectedFilter == filter;

          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: GestureDetector(
              onTap: () => onFilterSelected(filter),
              child: Column(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? colorScheme.primary.withValues(alpha: 0.1)
                          : colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(8),
                      border: isSelected
                          ? Border.all(color: colorScheme.primary, width: 2)
                          : null,
                    ),
                    child: Icon(
                      Icons.photo_filter,
                      color: isSelected 
                          ? colorScheme.primary
                          : colorScheme.onSurface.withValues(alpha: 0.6),
                      size: 20,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    name,
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: isSelected 
                          ? colorScheme.primary
                          : colorScheme.onSurface.withValues(alpha: 0.8),
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
