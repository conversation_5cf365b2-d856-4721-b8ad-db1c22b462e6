import 'package:flutter/material.dart';
import 'controllers/image_editor_controller.dart';
import 'models/editor_models.dart';
import 'widgets/filter_selector.dart';
import 'widgets/adjustment_tools.dart';
import '../../core/services/service_locator.dart';

/// Editor Screen
///
/// Provides comprehensive image editing capabilities including filters,
/// adjustments, cropping, and other image manipulation tools.
class EditorScreen extends StatefulWidget {
  final String imageId;

  const EditorScreen({super.key, required this.imageId});

  @override
  State<EditorScreen> createState() => _EditorScreenState();
}

class _EditorScreenState extends State<EditorScreen> {
  late ImageEditorController _controller;
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _controller = getIt<ImageEditorController>();
    _controller.addListener(_onControllerChanged);
    _initializeEditor();
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onControllerChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _initializeEditor() async {
    await _controller.initialize(widget.imageId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _buildAppBar(context),
      body: _controller.isLoading
          ? const Center(child: CircularProgressIndicator())
          : _controller.error != null
          ? _buildErrorView()
          : _buildEditorView(),
      bottomNavigationBar: _buildBottomTools(),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.black,
      foregroundColor: Colors.white,
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: const Text('编辑图片'),
      actions: [
        // Undo button
        Container(
          margin: const EdgeInsets.only(right: 4),
          decoration: BoxDecoration(
            color: _controller.canUndo
                ? const Color(0xFF6366F1).withValues(alpha: 0.2)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            icon: const Icon(Icons.undo),
            onPressed: _controller.canUndo ? _controller.undo : null,
            color: _controller.canUndo ? const Color(0xFF6366F1) : Colors.grey,
          ),
        ),

        // Redo button
        Container(
          margin: const EdgeInsets.only(right: 8),
          decoration: BoxDecoration(
            color: _controller.canRedo
                ? const Color(0xFF6366F1).withValues(alpha: 0.2)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            icon: const Icon(Icons.redo),
            onPressed: _controller.canRedo ? _controller.redo : null,
            color: _controller.canRedo ? const Color(0xFF6366F1) : Colors.grey,
          ),
        ),

        // Save button
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: ElevatedButton(
            onPressed: _controller.hasChanges
                ? () => _saveImage(context)
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _controller.hasChanges
                  ? const Color(0xFF6366F1)
                  : Colors.grey.withValues(alpha: 0.3),
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: const Text('保存'),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.white54),
          const SizedBox(height: 16),
          Text(
            _controller.error ?? '加载图片失败',
            style: const TextStyle(color: Colors.white70),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _initializeEditor(),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildEditorView() {
    return Column(
      children: [
        // Image preview area
        Expanded(child: _buildImagePreview()),

        // Tool tabs
        _buildToolTabs(),
      ],
    );
  }

  Widget _buildImagePreview() {
    if (_controller.currentImage == null) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    return Container(
      width: double.infinity,
      height: double.infinity,
      child: InteractiveViewer(
        minScale: 0.5,
        maxScale: 3.0,
        child: Center(
          child: Transform.rotate(
            angle: _controller.rotation * 3.14159 / 180,
            child: Image.memory(
              _controller.currentImage!.bytes,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.white54,
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildToolTabs() {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Colors.black87,
        border: Border(
          top: BorderSide(color: Colors.white.withValues(alpha: 0.1)),
        ),
      ),
      child: Row(
        children: [
          _buildTabButton(
            icon: Icons.photo_filter,
            label: '滤镜',
            index: 0,
            isSelected: _selectedTabIndex == 0,
          ),
          _buildTabButton(
            icon: Icons.tune,
            label: '调整',
            index: 1,
            isSelected: _selectedTabIndex == 1,
          ),
          _buildTabButton(
            icon: Icons.crop,
            label: '裁剪',
            index: 2,
            isSelected: _selectedTabIndex == 2,
          ),
          _buildTabButton(
            icon: Icons.transform,
            label: '变换',
            index: 3,
            isSelected: _selectedTabIndex == 3,
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton({
    required IconData icon,
    required String label,
    required int index,
    required bool isSelected,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedTabIndex = index;
          });
        },
        child: Container(
          height: double.infinity,
          decoration: BoxDecoration(
            color: isSelected
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.transparent,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.white : Colors.white60,
                size: 20,
              ),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.white60,
                  fontSize: 11,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget? _buildBottomTools() {
    switch (_selectedTabIndex) {
      case 0: // Filters
        return FilterSelector(
          selectedFilter: _controller.selectedFilter,
          intensity: _controller.filterIntensity,
          onFilterSelected: (filter) {
            if (filter != null) {
              _controller.applyFilter(
                filter,
                intensity: _controller.filterIntensity,
              );
            } else {
              // Reset filter
              _controller.reset();
            }
          },
          onIntensityChanged: (intensity) {
            if (_controller.selectedFilter != null) {
              _controller.applyFilter(
                _controller.selectedFilter!,
                intensity: intensity,
              );
            }
          },
        );

      case 1: // Adjustments
        return AdjustmentTools(
          brightness: _controller.brightness,
          contrast: _controller.contrast,
          saturation: _controller.saturation,
          onBrightnessChanged: _controller.adjustBrightness,
          onContrastChanged: _controller.adjustContrast,
          onSaturationChanged: _controller.adjustSaturation,
          onReset: () {
            _controller.reset();
          },
        );

      case 2: // Crop
        return _buildCropTools();

      case 3: // Transform
        return _buildTransformTools();

      default:
        return null;
    }
  }

  Widget _buildCropTools() {
    return Container(
      height: 120,
      color: Colors.black87,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(
            '裁剪功能即将推出',
            style: TextStyle(color: Colors.white70, fontSize: 16),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildCropButton('1:1', () {}),
              _buildCropButton('4:3', () {}),
              _buildCropButton('16:9', () {}),
              _buildCropButton('自由', () {}),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCropButton(String label, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          style: const TextStyle(color: Colors.white, fontSize: 14),
        ),
      ),
    );
  }

  Widget _buildTransformTools() {
    return Container(
      height: 120,
      color: Colors.black87,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(
            '变换工具',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildTransformButton(
                icon: Icons.rotate_left,
                label: '左转90°',
                onTap: () => _controller.rotateImage(-90),
              ),
              _buildTransformButton(
                icon: Icons.rotate_right,
                label: '右转90°',
                onTap: () => _controller.rotateImage(90),
              ),
              _buildTransformButton(
                icon: Icons.flip,
                label: '水平翻转',
                onTap: () {
                  // TODO: Implement flip
                },
              ),
              _buildTransformButton(
                icon: Icons.flip,
                label: '垂直翻转',
                onTap: () {
                  // TODO: Implement flip
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTransformButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.white, size: 20),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(color: Colors.white70, fontSize: 11),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _saveImage(BuildContext context) async {
    try {
      final savedImage = await _controller.saveImage();
      if (!context.mounted) return;

      if (savedImage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('图片已保存'), backgroundColor: Colors.green),
        );
        // 使用延迟确保SnackBar显示后再导航
        await Future.delayed(const Duration(milliseconds: 500));
        if (context.mounted && Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}
