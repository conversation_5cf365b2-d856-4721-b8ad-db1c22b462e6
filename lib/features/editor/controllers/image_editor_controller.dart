import 'package:flutter/foundation.dart';
import '../../../core/services/image_service.dart';
import '../models/editor_models.dart';

/// Image editor controller
class ImageEditorController extends ChangeNotifier {
  final ImageService _imageService;

  ImageEditorController(this._imageService);

  // State
  bool _isLoading = false;
  String? _error;
  ImageData? _originalImage;
  ImageData? _currentImage;
  List<EditingStep> _history = [];
  int _currentHistoryIndex = -1;

  // Current editing state
  FilterType? _selectedFilter;
  double _filterIntensity = 1.0;
  double _brightness = 0.0;
  double _contrast = 1.0;
  double _saturation = 1.0;
  double _rotation = 0.0;
  bool _isPreviewMode = false;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  ImageData? get originalImage => _originalImage;
  ImageData? get currentImage => _currentImage;
  FilterType? get selectedFilter => _selectedFilter;
  double get filterIntensity => _filterIntensity;
  double get brightness => _brightness;
  double get contrast => _contrast;
  double get saturation => _saturation;
  double get rotation => _rotation;
  bool get isPreviewMode => _isPreviewMode;
  bool get canUndo => _currentHistoryIndex >= 0;
  bool get canRedo => _currentHistoryIndex < _history.length - 1;
  bool get hasChanges => _currentHistoryIndex >= 0;

  /// Initialize with image path
  Future<void> initialize(String imagePath) async {
    try {
      _setLoading(true);
      _clearError();

      final imageData = await _imageService.loadImageFromPath(imagePath);
      if (imageData != null) {
        _originalImage = imageData;
        _currentImage = imageData;
        notifyListeners();
      } else {
        _setError('Failed to load image');
      }
    } catch (e) {
      _setError('Error loading image: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Apply filter
  Future<void> applyFilter(FilterType filter, {double intensity = 1.0}) async {
    if (_currentImage == null || _originalImage == null) return;

    try {
      _setLoading(true);
      _clearError();

      // Apply filter to the original image to get clean result
      final filteredImage = await _applyFilterToImage(
        _originalImage!,
        filter,
        intensity,
      );

      if (filteredImage != null) {
        _currentImage = filteredImage;
        _selectedFilter = filter;
        _filterIntensity = intensity;

        _addToHistory(
          EditingStep(
            type: EditingStepType.filter,
            filterType: filter,
            filterIntensity: intensity,
          ),
        );
      }

      notifyListeners();
    } catch (e) {
      _setError('Failed to apply filter: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Apply filter to image data
  Future<ImageData?> _applyFilterToImage(
    ImageData imageData,
    FilterType filter,
    double intensity,
  ) async {
    try {
      // Create a simple filter effect by creating a new ImageData with filter metadata
      // In a real implementation, this would call the Rust backend to actually apply the filter
      final filteredImageData = ImageData(
        path: imageData.path,
        width: imageData.width,
        height: imageData.height,
        bytes: imageData.bytes, // Keep original bytes for now
        fileName: '${filter.toString().toLowerCase()}_${imageData.fileName}',
        fileSize: imageData.fileSize,
        assetId: imageData.assetId,
      );

      return filteredImageData;
    } catch (e) {
      _setError('Failed to process filter: $e');
      return null;
    }
  }

  /// Adjust brightness
  Future<void> adjustBrightness(double value) async {
    if (_currentImage == null) return;

    try {
      _setLoading(true);
      _clearError();

      _brightness = value.clamp(-100.0, 100.0);

      _addToHistory(
        EditingStep(type: EditingStepType.brightness, brightness: _brightness),
      );

      notifyListeners();
    } catch (e) {
      _setError('Failed to adjust brightness: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Adjust contrast
  Future<void> adjustContrast(double value) async {
    if (_currentImage == null) return;

    try {
      _setLoading(true);
      _clearError();

      _contrast = value.clamp(0.0, 2.0);

      _addToHistory(
        EditingStep(type: EditingStepType.contrast, contrast: _contrast),
      );

      notifyListeners();
    } catch (e) {
      _setError('Failed to adjust contrast: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Adjust saturation
  Future<void> adjustSaturation(double value) async {
    if (_currentImage == null) return;

    try {
      _setLoading(true);
      _clearError();

      _saturation = value.clamp(0.0, 2.0);

      _addToHistory(
        EditingStep(type: EditingStepType.saturation, saturation: _saturation),
      );

      notifyListeners();
    } catch (e) {
      _setError('Failed to adjust saturation: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Rotate image
  Future<void> rotateImage(double angle) async {
    if (_currentImage == null) return;

    try {
      _setLoading(true);
      _clearError();

      _rotation = (_rotation + angle) % 360;

      _addToHistory(
        EditingStep(type: EditingStepType.rotation, rotation: _rotation),
      );

      notifyListeners();
    } catch (e) {
      _setError('Failed to rotate image: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Toggle preview mode
  void togglePreviewMode() {
    _isPreviewMode = !_isPreviewMode;
    notifyListeners();
  }

  /// Undo last operation
  void undo() {
    if (canUndo) {
      _currentHistoryIndex--;
      _rebuildImageFromHistory();
      notifyListeners();
    }
  }

  /// Redo last undone operation
  void redo() {
    if (canRedo) {
      _currentHistoryIndex++;
      _rebuildImageFromHistory();
      notifyListeners();
    }
  }

  /// Reset all changes
  void reset() {
    _currentImage = _originalImage;
    _history.clear();
    _currentHistoryIndex = -1;
    _selectedFilter = null;
    _filterIntensity = 1.0;
    _brightness = 0.0;
    _contrast = 1.0;
    _saturation = 1.0;
    _rotation = 0.0;
    _isPreviewMode = false;
    notifyListeners();
  }

  /// Save edited image
  Future<ImageData?> saveImage() async {
    if (_currentImage == null || !hasChanges) return _currentImage;

    try {
      _setLoading(true);
      _clearError();

      // TODO: Apply all changes to the image using Rust backend
      // For now, return the current image
      return _currentImage;
    } catch (e) {
      _setError('Failed to save image: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  void _addToHistory(EditingStep step) {
    // Remove any steps after current index (for redo functionality)
    if (_currentHistoryIndex < _history.length - 1) {
      _history = _history.sublist(0, _currentHistoryIndex + 1);
    }

    _history.add(step);
    _currentHistoryIndex = _history.length - 1;
  }

  void _rebuildImageFromHistory() {
    // TODO: Rebuild image by applying all steps up to current index
    // For now, just update the state based on the last step
    if (_currentHistoryIndex >= 0 && _currentHistoryIndex < _history.length) {
      final step = _history[_currentHistoryIndex];
      switch (step.type) {
        case EditingStepType.filter:
          _selectedFilter = step.filterType;
          _filterIntensity = step.filterIntensity ?? 1.0;
          break;
        case EditingStepType.brightness:
          _brightness = step.brightness ?? 0.0;
          break;
        case EditingStepType.contrast:
          _contrast = step.contrast ?? 1.0;
          break;
        case EditingStepType.saturation:
          _saturation = step.saturation ?? 1.0;
          break;
        case EditingStepType.rotation:
          _rotation = step.rotation ?? 0.0;
          break;
        case EditingStepType.crop:
        case EditingStepType.resize:
          // TODO: Implement crop and resize
          break;
      }
    }
  }
}
