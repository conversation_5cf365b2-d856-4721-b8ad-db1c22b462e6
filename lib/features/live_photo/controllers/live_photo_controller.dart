import 'package:flutter/foundation.dart';
import '../../../core/services/image_service.dart';
import '../live_photo_screen.dart';

/// Live photo controller
class LivePhotoController extends ChangeNotifier {
  final ImageService _imageService;

  LivePhotoController(this._imageService);

  // State
  bool _isLoading = false;
  String? _error;
  String? _sourceImagePath;
  String? _sourceVideoPath;
  LivePhotoEffectType? _selectedEffect;
  double _duration = 3.0;
  double _intensity = 1.0;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get sourceImagePath => _sourceImagePath;
  String? get sourceVideoPath => _sourceVideoPath;
  LivePhotoEffectType? get selectedEffect => _selectedEffect;
  double get duration => _duration;
  double get intensity => _intensity;
  bool get canGenerate =>
      (_sourceImagePath != null || _sourceVideoPath != null) &&
      _selectedEffect != null;

  /// Initialize from image
  Future<void> initializeFromImage(String imagePath) async {
    try {
      _setLoading(true);
      _clearError();

      // Validate image exists
      final imageData = await _imageService.loadImageFromPath(imagePath);
      if (imageData != null) {
        _sourceImagePath = imagePath;
        _sourceVideoPath = null;
        notifyListeners();
      } else {
        _setError('Failed to load image');
      }
    } catch (e) {
      _setError('Error loading image: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Initialize from video
  Future<void> initializeFromVideo(String videoPath) async {
    try {
      _setLoading(true);
      _clearError();

      // TODO: Validate video exists and extract frames
      _sourceVideoPath = videoPath;
      _sourceImagePath = null;
      notifyListeners();
    } catch (e) {
      _setError('Error loading video: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Select animation effect
  void selectEffect(LivePhotoEffectType effect) {
    _selectedEffect = effect;
    notifyListeners();
  }

  /// Set animation duration
  void setDuration(double duration) {
    _duration = duration.clamp(1.0, 5.0);
    notifyListeners();
  }

  /// Set effect intensity
  void setIntensity(double intensity) {
    _intensity = intensity.clamp(0.1, 2.0);
    notifyListeners();
  }

  /// Generate live photo
  Future<ImageData?> generateLivePhoto() async {
    if (!canGenerate) {
      _setError('Cannot generate: missing source or effect');
      return null;
    }

    try {
      _setLoading(true);
      _clearError();

      // TODO: Call Rust backend to generate live photo
      // For now, simulate the process
      await Future.delayed(const Duration(seconds: 2));

      // Return placeholder result
      if (_sourceImagePath != null) {
        return await _imageService.loadImageFromPath(_sourceImagePath!);
      }

      return null;
    } catch (e) {
      _setError('Failed to generate live photo: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Preview effect (for real-time preview)
  Future<void> previewEffect() async {
    if (_selectedEffect == null) return;

    try {
      // TODO: Generate preview frames for the selected effect
      // This would show a quick preview of the animation
    } catch (e) {
      _setError('Failed to preview effect: $e');
    }
  }

  /// Get available effects
  List<LivePhotoEffectType> getAvailableEffects() {
    return LivePhotoEffectType.values;
  }

  /// Get effect parameters for the selected effect
  Map<String, dynamic> getEffectParameters() {
    if (_selectedEffect == null) return {};

    switch (_selectedEffect!) {
      case LivePhotoEffectType.zoom:
        return {
          'type': 'zoom',
          'scale_range': [0.95, 1.05],
          'duration': _duration,
          'intensity': _intensity,
        };
      case LivePhotoEffectType.sway:
        return {
          'type': 'sway',
          'angle_range': [-2.0, 2.0],
          'duration': _duration,
          'intensity': _intensity,
        };
      case LivePhotoEffectType.breathe:
        return {
          'type': 'breathe',
          'scale_range': [0.98, 1.02],
          'duration': _duration,
          'intensity': _intensity,
          'easing': 'ease_in_out',
        };
      case LivePhotoEffectType.rotate:
        return {
          'type': 'rotate',
          'angle_range': [-1.0, 1.0],
          'duration': _duration,
          'intensity': _intensity,
        };
      case LivePhotoEffectType.float:
        return {
          'type': 'float',
          'offset_range': [-5.0, 5.0],
          'duration': _duration,
          'intensity': _intensity,
        };
    }
  }

  /// Reset to initial state
  void reset() {
    _sourceImagePath = null;
    _sourceVideoPath = null;
    _selectedEffect = null;
    _duration = 3.0;
    _intensity = 1.0;
    _clearError();
    notifyListeners();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}

/// Live photo generation options
class LivePhotoOptions {
  final LivePhotoEffectType effectType;
  final double duration;
  final double intensity;
  final int frameRate;
  final Map<String, dynamic> customParameters;

  const LivePhotoOptions({
    required this.effectType,
    this.duration = 3.0,
    this.intensity = 1.0,
    this.frameRate = 30,
    this.customParameters = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'effect_type': effectType.toString().split('.').last,
      'duration': duration,
      'intensity': intensity,
      'frame_rate': frameRate,
      'custom_parameters': customParameters,
    };
  }
}

/// Live photo result
class LivePhotoResult {
  final String outputPath;
  final int frameCount;
  final double actualDuration;
  final Map<String, dynamic> metadata;

  const LivePhotoResult({
    required this.outputPath,
    required this.frameCount,
    required this.actualDuration,
    this.metadata = const {},
  });

  factory LivePhotoResult.fromJson(Map<String, dynamic> json) {
    return LivePhotoResult(
      outputPath: json['output_path'] as String,
      frameCount: json['frame_count'] as int,
      actualDuration: (json['actual_duration'] as num).toDouble(),
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
    );
  }
}
