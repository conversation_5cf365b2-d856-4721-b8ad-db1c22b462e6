import 'package:flutter/material.dart';
import 'dart:io';
import 'controllers/live_photo_controller.dart';
import '../../core/services/service_locator.dart';

/// Live Photo Screen
///
/// Converts images or videos to live photos with motion effects
class LivePhotoScreen extends StatefulWidget {
  final String? imageId;
  final String? videoId;

  const LivePhotoScreen({super.key, this.imageId, this.videoId});

  @override
  State<LivePhotoScreen> createState() => _LivePhotoScreenState();
}

class _LivePhotoScreenState extends State<LivePhotoScreen> {
  late LivePhotoController _controller;
  int _selectedEffectIndex = 0;

  final List<LivePhotoEffect> _effects = [
    LivePhotoEffect(
      name: '缩放',
      description: '轻微的缩放动画效果',
      type: LivePhotoEffectType.zoom,
      icon: Icons.zoom_in,
    ),
    LivePhotoEffect(
      name: '摇摆',
      description: '左右轻微摇摆效果',
      type: LivePhotoEffectType.sway,
      icon: Icons.swap_horiz,
    ),
    LivePhotoEffect(
      name: '呼吸',
      description: '呼吸般的缩放效果',
      type: LivePhotoEffectType.breathe,
      icon: Icons.favorite,
    ),
    LivePhotoEffect(
      name: '旋转',
      description: '轻微的旋转动画',
      type: LivePhotoEffectType.rotate,
      icon: Icons.rotate_right,
    ),
    LivePhotoEffect(
      name: '浮动',
      description: '上下浮动效果',
      type: LivePhotoEffectType.float,
      icon: Icons.keyboard_arrow_up,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _controller = LivePhotoController(getIt.get());
    _controller.addListener(_onControllerChanged);
    _initializeLivePhoto();
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onControllerChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _initializeLivePhoto() async {
    if (widget.imageId != null) {
      await _controller.initializeFromImage(widget.imageId!);
    } else if (widget.videoId != null) {
      await _controller.initializeFromVideo(widget.videoId!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _buildAppBar(context),
      body: _controller.isLoading
          ? const Center(child: CircularProgressIndicator())
          : _controller.error != null
          ? _buildErrorView()
          : _buildLivePhotoView(),
      bottomNavigationBar: _buildBottomControls(),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.black,
      foregroundColor: Colors.white,
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: const Text('Live图生成'),
      actions: [
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: ElevatedButton(
            onPressed: _controller.canGenerate
                ? () => _generateLivePhoto(context)
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _controller.canGenerate
                  ? const Color(0xFF6366F1)
                  : Colors.grey.withValues(alpha: 0.3),
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: const Text('生成'),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.white54),
          const SizedBox(height: 16),
          Text(
            _controller.error ?? '加载失败',
            style: const TextStyle(color: Colors.white70),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _initializeLivePhoto(),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildLivePhotoView() {
    return Column(
      children: [
        // Preview area
        Expanded(flex: 3, child: _buildPreviewArea()),

        // Effect selector
        Expanded(flex: 1, child: _buildEffectSelector()),
      ],
    );
  }

  Widget _buildPreviewArea() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: _buildPreviewContent(),
      ),
    );
  }

  Widget _buildPreviewContent() {
    if (widget.imageId != null) {
      return Image.file(
        File(widget.imageId!),
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return const Center(
            child: Icon(Icons.error_outline, size: 64, color: Colors.white54),
          );
        },
      );
    } else if (widget.videoId != null) {
      // TODO: Implement video preview
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.video_library, size: 64, color: Colors.white54),
            SizedBox(height: 16),
            Text('视频预览', style: TextStyle(color: Colors.white70)),
          ],
        ),
      );
    } else {
      return const Center(
        child: Text('请选择图片或视频', style: TextStyle(color: Colors.white70)),
      );
    }
  }

  Widget _buildEffectSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '选择动画效果',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _effects.length,
              itemBuilder: (context, index) {
                final effect = _effects[index];
                final isSelected = _selectedEffectIndex == index;

                return Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedEffectIndex = index;
                      });
                      _controller.selectEffect(effect.type);
                    },
                    child: _buildEffectCard(effect, isSelected),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEffectCard(LivePhotoEffect effect, bool isSelected) {
    return Container(
      width: 120,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isSelected
            ? Colors.blue.withValues(alpha: 0.2)
            : Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
        border: isSelected ? Border.all(color: Colors.blue, width: 2) : null,
      ),
      child: Column(
        children: [
          Icon(
            effect.icon,
            color: isSelected ? Colors.blue : Colors.white70,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            effect.name,
            style: TextStyle(
              color: isSelected ? Colors.blue : Colors.white,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            effect.description,
            style: TextStyle(color: Colors.white60, fontSize: 11),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget? _buildBottomControls() {
    if (!_controller.canGenerate) return null;

    return Container(
      height: 80,
      color: Colors.black87,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Duration slider
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '动画时长: ${_controller.duration.toStringAsFixed(1)}秒',
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                ),
                Slider(
                  value: _controller.duration,
                  min: 1.0,
                  max: 5.0,
                  divisions: 8,
                  onChanged: _controller.setDuration,
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // Generate button
          ElevatedButton.icon(
            onPressed: () => _generateLivePhoto(context),
            icon: const Icon(Icons.play_arrow),
            label: const Text('生成Live图'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _generateLivePhoto(BuildContext context) async {
    try {
      final result = await _controller.generateLivePhoto();
      if (result != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Live图生成成功'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('生成失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}

/// Live photo effect definition
class LivePhotoEffect {
  final String name;
  final String description;
  final LivePhotoEffectType type;
  final IconData icon;

  const LivePhotoEffect({
    required this.name,
    required this.description,
    required this.type,
    required this.icon,
  });
}

/// Live photo effect types
enum LivePhotoEffectType { zoom, sway, breathe, rotate, float }
