import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'dart:io';
import 'controllers/format_converter_controller.dart';
import 'models/format_models.dart';
import '../../core/services/service_locator.dart';

/// Format Converter Screen
/// 
/// Converts images between different formats with quality and size options
class FormatConverterScreen extends StatefulWidget {
  final String? imageId;

  const FormatConverterScreen({
    super.key,
    this.imageId,
  });

  @override
  State<FormatConverterScreen> createState() => _FormatConverterScreenState();
}

class _FormatConverterScreenState extends State<FormatConverterScreen> {
  late FormatConverterController _controller;

  @override
  void initState() {
    super.initState();
    _controller = FormatConverterController(getIt.get());
    _controller.addListener(_onControllerChanged);
    if (widget.imageId != null) {
      _initializeConverter();
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onControllerChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _initializeConverter() async {
    if (widget.imageId != null) {
      await _controller.initialize(widget.imageId!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: _controller.isLoading
          ? const Center(child: CircularProgressIndicator())
          : _controller.error != null
              ? _buildErrorView()
              : _buildConverterView(),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: () => context.pop(),
      ),
      title: const Text('格式转换'),
      actions: [
        TextButton(
          onPressed: _controller.canConvert ? () => _convertImage(context) : null,
          child: Text(
            '转换',
            style: TextStyle(
              color: _controller.canConvert ? Theme.of(context).colorScheme.primary : Colors.grey,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            _controller.error ?? '加载失败',
            style: const TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _initializeConverter(),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildConverterView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image preview
          if (widget.imageId != null) _buildImagePreview(),
          
          const SizedBox(height: 24),
          
          // Source format info
          _buildSourceFormatInfo(),
          
          const SizedBox(height: 24),
          
          // Target format selector
          _buildTargetFormatSelector(),
          
          const SizedBox(height: 24),
          
          // Quality settings
          if (_controller.targetFormat?.supportsQuality == true)
            _buildQualitySettings(),
          
          const SizedBox(height: 24),
          
          // Size settings
          _buildSizeSettings(),
          
          const SizedBox(height: 24),
          
          // Advanced options
          _buildAdvancedOptions(),
        ],
      ),
    );
  }

  Widget _buildImagePreview() {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.file(
          File(widget.imageId!),
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return const Center(
              child: Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.grey,
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSourceFormatInfo() {
    final sourceFormat = _controller.sourceFormat;
    if (sourceFormat == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '原始格式',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.image,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  sourceFormat.displayName,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const Spacer(),
                if (_controller.sourceFileSize != null)
                  Text(
                    _formatFileSize(_controller.sourceFileSize!),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
            if (sourceFormat.description.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                sourceFormat.description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTargetFormatSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '目标格式',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: ImageFormat.values.map((format) {
                final isSelected = _controller.targetFormat == format;
                return GestureDetector(
                  onTap: () => _controller.setTargetFormat(format),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                          : Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: isSelected
                          ? Border.all(color: Theme.of(context).colorScheme.primary)
                          : null,
                    ),
                    child: Column(
                      children: [
                        Text(
                          format.displayName,
                          style: TextStyle(
                            color: isSelected 
                                ? Theme.of(context).colorScheme.primary
                                : Colors.black87,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                        if (format.description.isNotEmpty) ...[
                          const SizedBox(height: 4),
                          Text(
                            format.description,
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQualitySettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '质量设置',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                const Icon(Icons.high_quality, size: 20),
                const SizedBox(width: 8),
                Text('质量: ${(_controller.quality * 100).round()}%'),
                const Spacer(),
                Text(
                  _getQualityLabel(_controller.quality),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            Slider(
              value: _controller.quality,
              min: 0.1,
              max: 1.0,
              divisions: 9,
              onChanged: _controller.setQuality,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSizeSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '尺寸设置',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            // Size presets
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildSizePreset('原始尺寸', null),
                _buildSizePreset('50%', 0.5),
                _buildSizePreset('75%', 0.75),
                _buildSizePreset('150%', 1.5),
                _buildSizePreset('200%', 2.0),
              ],
            ),
            
            if (_controller.customScale != null) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  const Icon(Icons.photo_size_select_large, size: 20),
                  const SizedBox(width: 8),
                  Text('缩放: ${(_controller.customScale! * 100).round()}%'),
                ],
              ),
              const SizedBox(height: 8),
              Slider(
                value: _controller.customScale!,
                min: 0.1,
                max: 3.0,
                divisions: 29,
                onChanged: _controller.setCustomScale,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSizePreset(String label, double? scale) {
    final isSelected = _controller.customScale == scale;
    
    return GestureDetector(
      onTap: () => _controller.setCustomScale(scale),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected 
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Colors.grey[100],
          borderRadius: BorderRadius.circular(6),
          border: isSelected
              ? Border.all(color: Theme.of(context).colorScheme.primary)
              : null,
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected 
                ? Theme.of(context).colorScheme.primary
                : Colors.black87,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            fontSize: 13,
          ),
        ),
      ),
    );
  }

  Widget _buildAdvancedOptions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '高级选项',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            CheckboxListTile(
              title: const Text('保持EXIF信息'),
              subtitle: const Text('保留照片的拍摄信息和元数据'),
              value: _controller.preserveExif,
              onChanged: _controller.setPreserveExif,
              contentPadding: EdgeInsets.zero,
            ),
            
            CheckboxListTile(
              title: const Text('优化文件大小'),
              subtitle: const Text('使用压缩算法减小文件大小'),
              value: _controller.optimizeSize,
              onChanged: _controller.setOptimizeSize,
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }

  String _getQualityLabel(double quality) {
    if (quality >= 0.9) return '最高';
    if (quality >= 0.7) return '高';
    if (quality >= 0.5) return '中等';
    if (quality >= 0.3) return '低';
    return '最低';
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  Future<void> _convertImage(BuildContext context) async {
    try {
      final result = await _controller.convertImage();
      if (result != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('格式转换成功'),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('转换失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
