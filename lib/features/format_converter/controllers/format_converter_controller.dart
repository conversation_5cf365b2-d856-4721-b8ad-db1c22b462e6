import 'package:flutter/foundation.dart';
import 'dart:io';
import '../../../core/services/image_service.dart';
import '../models/format_models.dart';

/// Format converter controller
class FormatConverterController extends ChangeNotifier {
  final ImageService _imageService;

  FormatConverterController(this._imageService);

  // State
  bool _isLoading = false;
  String? _error;
  String? _sourceImagePath;
  ImageFormat? _sourceFormat;
  ImageFormat? _targetFormat;
  int? _sourceFileSize;
  double _quality = 0.9;
  double? _customScale;
  bool _preserveExif = true;
  bool _optimizeSize = false;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get sourceImagePath => _sourceImagePath;
  ImageFormat? get sourceFormat => _sourceFormat;
  ImageFormat? get targetFormat => _targetFormat;
  int? get sourceFileSize => _sourceFileSize;
  double get quality => _quality;
  double? get customScale => _customScale;
  bool get preserveExif => _preserveExif;
  bool get optimizeSize => _optimizeSize;
  bool get canConvert => _sourceImagePath != null && _targetFormat != null;

  /// Initialize converter with source image
  Future<void> initialize(String imagePath) async {
    try {
      _setLoading(true);
      _clearError();

      // Load image and detect format
      final imageData = await _imageService.loadImageFromPath(imagePath);
      if (imageData != null) {
        _sourceImagePath = imagePath;
        _sourceFormat = _detectImageFormat(imagePath);
        _sourceFileSize = await _getFileSize(imagePath);
        
        // Set default target format (different from source)
        _setDefaultTargetFormat();
        
        notifyListeners();
      } else {
        _setError('Failed to load image');
      }
    } catch (e) {
      _setError('Error loading image: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Set target format
  void setTargetFormat(ImageFormat format) {
    _targetFormat = format;
    
    // Adjust quality setting based on format support
    if (!format.supportsQuality) {
      _quality = 1.0; // Use maximum quality for lossless formats
    }
    
    notifyListeners();
  }

  /// Set quality (0.1 to 1.0)
  void setQuality(double quality) {
    _quality = quality.clamp(0.1, 1.0);
    notifyListeners();
  }

  /// Set custom scale factor
  void setCustomScale(double? scale) {
    _customScale = scale;
    notifyListeners();
  }

  /// Set preserve EXIF option
  void setPreserveExif(bool? preserve) {
    _preserveExif = preserve ?? true;
    notifyListeners();
  }

  /// Set optimize size option
  void setOptimizeSize(bool? optimize) {
    _optimizeSize = optimize ?? false;
    notifyListeners();
  }

  /// Convert image with current settings
  Future<ConversionResult?> convertImage() async {
    if (!canConvert) {
      _setError('Cannot convert: missing source or target format');
      return null;
    }

    try {
      _setLoading(true);
      _clearError();

      final options = ConversionOptions(
        targetFormat: _targetFormat!,
        quality: _quality,
        scale: _customScale,
        preserveExif: _preserveExif,
        optimizeSize: _optimizeSize,
      );

      // TODO: Call Rust backend for actual conversion
      // For now, simulate the conversion process
      await Future.delayed(const Duration(seconds: 2));

      // Create mock result
      final result = ConversionResult(
        outputPath: _generateOutputPath(),
        sourceFormat: _sourceFormat!,
        targetFormat: _targetFormat!,
        originalSize: _sourceFileSize ?? 0,
        convertedSize: _estimateConvertedSize(),
        compressionRatio: _targetFormat!.typicalCompressionRatio,
        processingTime: const Duration(seconds: 2),
        metadata: {
          'quality': _quality,
          'scale': _customScale,
          'preserve_exif': _preserveExif,
          'optimize_size': _optimizeSize,
        },
      );

      return result;
    } catch (e) {
      _setError('Failed to convert image: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Get estimated output file size
  int getEstimatedOutputSize() {
    if (_sourceFileSize == null || _targetFormat == null) return 0;

    double estimatedSize = _sourceFileSize!.toDouble();

    // Apply format compression
    estimatedSize *= _targetFormat!.typicalCompressionRatio;

    // Apply quality factor for lossy formats
    if (_targetFormat!.supportsQuality) {
      estimatedSize *= _quality;
    }

    // Apply scale factor
    if (_customScale != null) {
      estimatedSize *= _customScale! * _customScale!; // Area scaling
    }

    // Apply optimization
    if (_optimizeSize) {
      estimatedSize *= 0.8; // Assume 20% additional reduction
    }

    return estimatedSize.round();
  }

  /// Get conversion options summary
  Map<String, String> getConversionSummary() {
    final summary = <String, String>{};

    if (_sourceFormat != null && _targetFormat != null) {
      summary['格式转换'] = '${_sourceFormat!.displayName} → ${_targetFormat!.displayName}';
    }

    if (_targetFormat?.supportsQuality == true) {
      summary['质量'] = '${(_quality * 100).round()}%';
    }

    if (_customScale != null) {
      summary['尺寸'] = '${(_customScale! * 100).round()}%';
    }

    if (_preserveExif) {
      summary['EXIF'] = '保留';
    }

    if (_optimizeSize) {
      summary['优化'] = '启用';
    }

    final estimatedSize = getEstimatedOutputSize();
    if (estimatedSize > 0 && _sourceFileSize != null) {
      final reduction = ((_sourceFileSize! - estimatedSize) / _sourceFileSize!) * 100;
      if (reduction > 0) {
        summary['预计减少'] = '${reduction.toStringAsFixed(1)}%';
      } else {
        summary['预计增加'] = '${(-reduction).toStringAsFixed(1)}%';
      }
    }

    return summary;
  }

  /// Reset all settings to defaults
  void reset() {
    _sourceImagePath = null;
    _sourceFormat = null;
    _targetFormat = null;
    _sourceFileSize = null;
    _quality = 0.9;
    _customScale = null;
    _preserveExif = true;
    _optimizeSize = false;
    _clearError();
    notifyListeners();
  }

  // Private methods
  ImageFormat? _detectImageFormat(String imagePath) {
    final extension = imagePath.split('.').last;
    return ImageFormat.fromExtension(extension);
  }

  Future<int> _getFileSize(String imagePath) async {
    try {
      final file = File(imagePath);
      return await file.length();
    } catch (e) {
      return 0;
    }
  }

  void _setDefaultTargetFormat() {
    if (_sourceFormat == null) return;

    // Choose a different format as default target
    switch (_sourceFormat!) {
      case ImageFormat.jpeg:
        _targetFormat = ImageFormat.png;
        break;
      case ImageFormat.png:
        _targetFormat = ImageFormat.jpeg;
        break;
      case ImageFormat.webp:
        _targetFormat = ImageFormat.jpeg;
        break;
      case ImageFormat.heic:
        _targetFormat = ImageFormat.jpeg;
        break;
      default:
        _targetFormat = ImageFormat.jpeg;
        break;
    }
  }

  String _generateOutputPath() {
    if (_sourceImagePath == null || _targetFormat == null) {
      return '';
    }

    final sourcePath = _sourceImagePath!;
    final directory = File(sourcePath).parent.path;
    final baseName = File(sourcePath).uri.pathSegments.last.split('.').first;
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return '$directory/${baseName}_converted_$timestamp.${_targetFormat!.extension}';
  }

  int _estimateConvertedSize() {
    if (_sourceFileSize == null || _targetFormat == null) return 0;

    double estimatedSize = _sourceFileSize!.toDouble();
    estimatedSize *= _targetFormat!.typicalCompressionRatio;

    if (_targetFormat!.supportsQuality) {
      estimatedSize *= _quality;
    }

    if (_customScale != null) {
      estimatedSize *= _customScale! * _customScale!;
    }

    if (_optimizeSize) {
      estimatedSize *= 0.8;
    }

    return estimatedSize.round();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  @override
  void dispose() {
    super.dispose();
  }
}
