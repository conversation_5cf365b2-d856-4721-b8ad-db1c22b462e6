import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/gallery_models.dart';
import '../../../core/services/service_locator.dart';
import '../../../core/services/storage_service.dart';
import 'dart:io';

/// Gallery controller for managing gallery state and operations
class GalleryController extends ChangeNotifier {
  // Services
  late final StorageService _storageService;

  // State
  List<GalleryItem> _allItems = [];
  List<GalleryItem> _filteredItems = [];
  GalleryFilter _currentFilter = GalleryFilter.all;
  GallerySortBy _sortBy = GallerySortBy.dateCreated;
  GallerySortOrder _sortOrder = GallerySortOrder.descending;
  GalleryViewMode _viewMode = GalleryViewMode.grid;
  GallerySelection _selection = const GallerySelection();
  GallerySearchState _searchState = const GallerySearchState();

  bool _isLoading = false;
  String? _error;

  // Debounce timer for search
  Timer? _searchDebounceTimer;

  // Getters
  List<GalleryItem> get allItems => List.unmodifiable(_allItems);
  List<GalleryItem> get filteredItems => List.unmodifiable(_filteredItems);
  List<GalleryItem> get displayItems =>
      _searchState.isActive ? _searchState.searchResults : _filteredItems;

  GalleryFilter get currentFilter => _currentFilter;
  GallerySortBy get sortBy => _sortBy;
  GallerySortOrder get sortOrder => _sortOrder;
  GalleryViewMode get viewMode => _viewMode;
  GallerySelection get selection => _selection;
  GallerySearchState get searchState => _searchState;

  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasItems => _allItems.isNotEmpty;
  bool get isSelectionMode => _selection.isSelectionMode;
  int get selectedCount => _selection.selectedCount;

  /// Initialize gallery and load items
  Future<void> initialize() async {
    _storageService = getIt<StorageService>();
    await loadItems();
  }

  /// Load gallery items
  Future<void> loadItems() async {
    _setLoading(true);
    _clearError();

    try {
      await _loadItemsFromStorage();
      _applyFilterAndSort();
    } catch (e) {
      _setError('Failed to load gallery items: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load items from storage
  Future<void> _loadItemsFromStorage() async {
    final storedItems = _storageService.getGalleryItems();
    _allItems = storedItems.map((itemData) {
      return GalleryItem(
        id: itemData['id'] as String,
        name: itemData['name'] as String,
        type: GalleryItemType.values.firstWhere(
          (type) => type.name == itemData['type'],
          orElse: () => GalleryItemType.image,
        ),
        path: itemData['path'] as String,
        thumbnailPath: itemData['thumbnailPath'] as String?,
        size: itemData['size'] as int,
        createdAt: DateTime.parse(itemData['createdAt'] as String),
        modifiedAt: DateTime.parse(itemData['modifiedAt'] as String),
        tags: List<String>.from(itemData['tags'] as List? ?? []),
        isFavorite: itemData['isFavorite'] as bool? ?? false,
      );
    }).toList();

    // If no items exist, create some sample items for demonstration
    if (_allItems.isEmpty) {
      _allItems = _generateMockItems();
      // Save mock items to storage for persistence
      for (final item in _allItems) {
        await _saveItemToStorage(item);
      }
    }
  }

  /// Save item to storage
  Future<void> _saveItemToStorage(GalleryItem item) async {
    final itemData = {
      'id': item.id,
      'name': item.name,
      'type': item.type.name,
      'path': item.path,
      'thumbnailPath': item.thumbnailPath,
      'size': item.size,
      'createdAt': item.createdAt.toIso8601String(),
      'modifiedAt': item.modifiedAt.toIso8601String(),
      'tags': item.tags,
      'isFavorite': item.isFavorite,
    };
    await _storageService.saveGalleryItem(itemData);
  }

  /// Add new item to gallery (public method for other features to use)
  Future<void> addItem(GalleryItem item) async {
    try {
      await _saveItemToStorage(item);
      _allItems.insert(0, item); // Add to beginning
      _applyFilterAndSort();
      notifyListeners();
    } catch (e) {
      _setError('Failed to add item to gallery: $e');
    }
  }

  /// Create and add a new gallery item from file path
  Future<void> addItemFromPath({
    required String filePath,
    required String name,
    required GalleryItemType type,
    String? thumbnailPath,
    List<String> tags = const [],
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('File does not exist: $filePath');
      }

      final stat = await file.stat();
      final now = DateTime.now();

      final item = GalleryItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        path: filePath,
        type: type,
        createdAt: now,
        modifiedAt: now,
        size: stat.size,
        thumbnailPath: thumbnailPath,
        tags: tags,
      );

      await addItem(item);
    } catch (e) {
      _setError('Failed to add item from path: $e');
    }
  }

  /// Refresh gallery items
  Future<void> refresh() async {
    await loadItems();
  }

  /// Set filter
  void setFilter(GalleryFilter filter) {
    if (_currentFilter != filter) {
      _currentFilter = filter;
      _applyFilterAndSort();
      notifyListeners();
    }
  }

  /// Set sort options
  void setSortBy(GallerySortBy sortBy, [GallerySortOrder? sortOrder]) {
    bool changed = false;

    if (_sortBy != sortBy) {
      _sortBy = sortBy;
      changed = true;
    }

    if (sortOrder != null && _sortOrder != sortOrder) {
      _sortOrder = sortOrder;
      changed = true;
    }

    if (changed) {
      _applyFilterAndSort();
      notifyListeners();
    }
  }

  /// Toggle sort order
  void toggleSortOrder() {
    _sortOrder = _sortOrder == GallerySortOrder.ascending
        ? GallerySortOrder.descending
        : GallerySortOrder.ascending;
    _applyFilterAndSort();
    notifyListeners();
  }

  /// Set view mode
  void setViewMode(GalleryViewMode viewMode) {
    if (_viewMode != viewMode) {
      _viewMode = viewMode;
      notifyListeners();
    }
  }

  /// Search items
  void search(String query) {
    _searchDebounceTimer?.cancel();

    _searchState = _searchState.copyWith(
      query: query,
      isSearching: query.isNotEmpty,
    );

    if (query.isEmpty) {
      _searchState = _searchState.copyWith(
        searchResults: [],
        isSearching: false,
      );
      notifyListeners();
      return;
    }

    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      _performSearch(query);
    });

    notifyListeners();
  }

  /// Clear search
  void clearSearch() {
    _searchDebounceTimer?.cancel();
    _searchState = const GallerySearchState();
    notifyListeners();
  }

  /// Toggle selection mode
  void toggleSelectionMode() {
    if (_selection.isSelectionMode) {
      _selection = const GallerySelection();
    } else {
      _selection = _selection.copyWith(isSelectionMode: true);
    }
    notifyListeners();
  }

  /// Select/deselect item
  void toggleItemSelection(String itemId) {
    if (!_selection.isSelectionMode) return;

    final selectedIds = Set<String>.from(_selection.selectedIds);
    if (selectedIds.contains(itemId)) {
      selectedIds.remove(itemId);
    } else {
      selectedIds.add(itemId);
    }

    _selection = _selection.copyWith(selectedIds: selectedIds);
    notifyListeners();
  }

  /// Select all items
  void selectAll() {
    if (!_selection.isSelectionMode) return;

    final allIds = displayItems.map((item) => item.id).toSet();
    _selection = _selection.copyWith(selectedIds: allIds);
    notifyListeners();
  }

  /// Clear selection
  void clearSelection() {
    _selection = _selection.copyWith(selectedIds: {});
    notifyListeners();
  }

  /// Toggle favorite status
  Future<void> toggleFavorite(String itemId) async {
    try {
      final itemIndex = _allItems.indexWhere((item) => item.id == itemId);
      if (itemIndex != -1) {
        final item = _allItems[itemIndex];
        final updatedItem = item.copyWith(isFavorite: !item.isFavorite);
        _allItems[itemIndex] = updatedItem;

        // Save to storage
        await _saveItemToStorage(updatedItem);

        _applyFilterAndSort();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update favorite: $e');
    }
  }

  /// Delete selected items
  Future<void> deleteSelectedItems() async {
    if (!_selection.isSelectionMode || _selection.selectedIds.isEmpty) return;

    try {
      _setLoading(true);

      // Delete files from storage
      await _storageService.deleteGalleryItems(_selection.selectedIds.toList());

      // Delete actual files if they exist
      for (final itemId in _selection.selectedIds) {
        final item = _allItems.firstWhere((item) => item.id == itemId);
        final file = File(item.path);
        if (await file.exists()) {
          try {
            await file.delete();
          } catch (e) {
            // Ignore file deletion errors, item is already removed from storage
            debugPrint('Failed to delete file ${item.path}: $e');
          }
        }
      }

      _allItems.removeWhere((item) => _selection.selectedIds.contains(item.id));

      _selection = const GallerySelection();
      _applyFilterAndSort();
    } catch (e) {
      _setError('Failed to delete items: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Apply current filter and sort
  void _applyFilterAndSort() {
    // Apply filter
    _filteredItems = _allItems.where((item) {
      switch (_currentFilter) {
        case GalleryFilter.all:
          return true;
        case GalleryFilter.images:
          return item.type == GalleryItemType.image;
        case GalleryFilter.collages:
          return item.type == GalleryItemType.collage;
        case GalleryFilter.livePhotos:
          return item.type == GalleryItemType.livePhoto;
        case GalleryFilter.videos:
          return item.type == GalleryItemType.video;
        case GalleryFilter.recent:
          final weekAgo = DateTime.now().subtract(const Duration(days: 7));
          return item.createdAt.isAfter(weekAgo);
        case GalleryFilter.favorites:
          return item.isFavorite;
      }
    }).toList();

    // Apply sort
    _filteredItems.sort((a, b) {
      int comparison;
      switch (_sortBy) {
        case GallerySortBy.dateCreated:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case GallerySortBy.dateModified:
          comparison = a.modifiedAt.compareTo(b.modifiedAt);
          break;
        case GallerySortBy.name:
          comparison = a.name.compareTo(b.name);
          break;
        case GallerySortBy.size:
          comparison = a.size.compareTo(b.size);
          break;
        case GallerySortBy.type:
          comparison = a.type.name.compareTo(b.type.name);
          break;
      }

      return _sortOrder == GallerySortOrder.ascending
          ? comparison
          : -comparison;
    });
  }

  /// Perform search operation
  void _performSearch(String query) {
    final results = _filteredItems.where((item) {
      final searchQuery = query.toLowerCase();
      return item.name.toLowerCase().contains(searchQuery) ||
          item.tags.any((tag) => tag.toLowerCase().contains(searchQuery));
    }).toList();

    _searchState = _searchState.copyWith(
      searchResults: results,
      isSearching: false,
    );
    notifyListeners();
  }

  /// Generate mock items for testing
  List<GalleryItem> _generateMockItems() {
    final now = DateTime.now();
    return List.generate(20, (index) {
      final type =
          GalleryItemType.values[index % GalleryItemType.values.length];
      return GalleryItem(
        id: 'item_$index',
        name: 'Item ${index + 1}',
        path: '/mock/path/item_$index',
        type: type,
        createdAt: now.subtract(Duration(days: index)),
        modifiedAt: now.subtract(Duration(hours: index)),
        size: (index + 1) * 1024 * 1024, // Size in bytes
        isFavorite: index % 5 == 0,
        tags: ['tag${index % 3}', 'category${index % 2}'],
      );
    });
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error state
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error state
  void _clearError() {
    _error = null;
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    super.dispose();
  }
}
