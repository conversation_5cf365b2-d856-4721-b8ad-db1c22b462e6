import 'package:flutter/material.dart';
import '../models/gallery_models.dart';
import '../../../core/design_system/colors.dart';

/// Gallery item card widget
class GalleryItemCard extends StatelessWidget {
  final GalleryItem item;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final VoidCallback onFavoriteToggle;
  final bool isListMode;
  final bool isMasonryMode;

  const GalleryItemCard({
    super.key,
    required this.item,
    required this.isSelected,
    required this.isSelectionMode,
    required this.onTap,
    required this.onLongPress,
    required this.onFavoriteToggle,
    this.isListMode = false,
    this.isMasonryMode = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isListMode) {
      return _buildListCard(context);
    }
    
    return _buildGridCard(context);
  }

  Widget _buildGridCard(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: isSelected 
                  ? TuShenColors.primary.withValues(alpha: 0.3)
                  : Colors.black.withValues(alpha: 0.1),
              blurRadius: isSelected ? 12 : 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Image/thumbnail
              _buildThumbnail(context),
              
              // Selection overlay
              if (isSelectionMode) _buildSelectionOverlay(context),
              
              // Type indicator
              _buildTypeIndicator(context),
              
              // Favorite indicator
              if (item.isFavorite) _buildFavoriteIndicator(context),
              
              // Selection checkbox
              if (isSelectionMode) _buildSelectionCheckbox(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildListCard(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected 
              ? TuShenColors.primary.withValues(alpha: 0.1)
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: isSelected
              ? Border.all(color: TuShenColors.primary, width: 2)
              : Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            // Thumbnail
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Stack(
                  children: [
                    _buildThumbnail(context),
                    _buildTypeIndicator(context),
                  ],
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Item info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          item.name,
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (item.isFavorite)
                        Icon(
                          Icons.favorite,
                          size: 16,
                          color: TuShenColors.accent,
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatFileSize(item.size),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                  Text(
                    _formatDate(item.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),
            
            // Selection checkbox
            if (isSelectionMode)
              Checkbox(
                value: isSelected,
                onChanged: (_) => onTap,
                activeColor: TuShenColors.primary,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildThumbnail(BuildContext context) {
    // For now, show a placeholder with type-specific icon
    return Container(
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Center(
        child: Icon(
          _getTypeIcon(),
          size: isListMode ? 24 : 48,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
        ),
      ),
    );
  }

  Widget _buildSelectionOverlay(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      color: isSelected 
          ? TuShenColors.primary.withValues(alpha: 0.3)
          : Colors.black.withValues(alpha: 0.1),
    );
  }

  Widget _buildTypeIndicator(BuildContext context) {
    if (item.type == GalleryItemType.image) return const SizedBox.shrink();
    
    return Positioned(
      top: 8,
      left: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getTypeIcon(),
              size: 12,
              color: Colors.white,
            ),
            const SizedBox(width: 2),
            Text(
              _getTypeName(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoriteIndicator(BuildContext context) {
    return Positioned(
      top: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          Icons.favorite,
          size: 12,
          color: TuShenColors.accent,
        ),
      ),
    );
  }

  Widget _buildSelectionCheckbox(BuildContext context) {
    return Positioned(
      bottom: 8,
      right: 8,
      child: AnimatedScale(
        scale: isSelectionMode ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 200),
        child: Container(
          decoration: BoxDecoration(
            color: isSelected ? TuShenColors.primary : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? TuShenColors.primary : Colors.grey,
              width: 2,
            ),
          ),
          child: Icon(
            isSelected ? Icons.check : null,
            size: 16,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  IconData _getTypeIcon() {
    switch (item.type) {
      case GalleryItemType.image:
        return Icons.image;
      case GalleryItemType.collage:
        return Icons.dashboard;
      case GalleryItemType.livePhoto:
        return Icons.motion_photos_on;
      case GalleryItemType.video:
        return Icons.videocam;
    }
  }

  String _getTypeName() {
    switch (item.type) {
      case GalleryItemType.image:
        return 'IMG';
      case GalleryItemType.collage:
        return 'COL';
      case GalleryItemType.livePhoto:
        return 'LIVE';
      case GalleryItemType.video:
        return 'VID';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
