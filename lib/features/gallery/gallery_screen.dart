import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'controllers/gallery_controller.dart';
import 'models/gallery_models.dart';
import 'widgets/gallery_grid.dart';
import 'widgets/gallery_search_bar.dart';
import 'widgets/gallery_filter_bar.dart';
import 'widgets/gallery_selection_bar.dart';
import '../../core/design_system/colors.dart';

/// Gallery Screen
///
/// Displays user's photos and created collages in an organized grid view.
/// Provides search, filtering, and organization capabilities.
class GalleryScreen extends StatefulWidget {
  const GalleryScreen({super.key});

  @override
  State<GalleryScreen> createState() => _GalleryScreenState();
}

class _GalleryScreenState extends State<GalleryScreen> {
  late final GalleryController _controller;
  bool _isSearchVisible = false;

  @override
  void initState() {
    super.initState();
    _controller = GalleryController();
    _controller.initialize();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return Scaffold(
          backgroundColor: Theme.of(context).colorScheme.surface,
          body: CustomScrollView(
            slivers: [
              _buildAppBar(context),
              if (_isSearchVisible) _buildSearchBar(),
              _buildFilterBar(),
              if (_controller.isSelectionMode) _buildSelectionBar(),
              _buildContent(),
            ],
          ),
          floatingActionButton: _buildFloatingActionButton(context),
        );
      },
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        title: _controller.isSelectionMode
            ? Text('${_controller.selectedCount} selected')
            : Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: TuShenColors.primaryGradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Text(
                  'Gallery',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
        titlePadding: const EdgeInsets.only(left: 16, bottom: 16),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: TuShenColors.primaryGradient,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      actions: _buildAppBarActions(context),
    );
  }

  List<Widget> _buildAppBarActions(BuildContext context) {
    if (_controller.isSelectionMode) {
      return [
        IconButton(
          icon: const Icon(Icons.select_all, color: Colors.white),
          onPressed: _controller.selectAll,
        ),
        IconButton(
          icon: const Icon(Icons.delete, color: Colors.white),
          onPressed: _controller.selectedCount > 0
              ? () => _showDeleteConfirmation(context)
              : null,
        ),
        IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: _controller.toggleSelectionMode,
        ),
      ];
    }

    return [
      IconButton(
        icon: Icon(
          _isSearchVisible ? Icons.search_off : Icons.search,
          color: Colors.white,
        ),
        onPressed: () {
          setState(() {
            _isSearchVisible = !_isSearchVisible;
          });
          if (!_isSearchVisible) {
            _controller.clearSearch();
          }
        },
      ),
      IconButton(
        icon: Icon(
          _controller.viewMode == GalleryViewMode.grid
              ? Icons.view_list
              : Icons.grid_view,
          color: Colors.white,
        ),
        onPressed: () {
          _controller.setViewMode(
            _controller.viewMode == GalleryViewMode.grid
                ? GalleryViewMode.list
                : GalleryViewMode.grid,
          );
        },
      ),
      PopupMenuButton<String>(
        icon: const Icon(Icons.more_vert, color: Colors.white),
        onSelected: _handleMenuAction,
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'sort',
            child: ListTile(
              leading: Icon(Icons.sort),
              title: Text('Sort'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
          const PopupMenuItem(
            value: 'select',
            child: ListTile(
              leading: Icon(Icons.checklist),
              title: Text('Select'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
          const PopupMenuItem(
            value: 'refresh',
            child: ListTile(
              leading: Icon(Icons.refresh),
              title: Text('Refresh'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
    ];
  }

  Widget _buildSearchBar() {
    return SliverToBoxAdapter(
      child: GallerySearchBar(
        controller: _controller,
        onSearch: _controller.search,
        onClear: _controller.clearSearch,
      ),
    );
  }

  Widget _buildFilterBar() {
    return SliverToBoxAdapter(
      child: GalleryFilterBar(
        controller: _controller,
        onFilterChanged: _controller.setFilter,
      ),
    );
  }

  Widget _buildSelectionBar() {
    return SliverToBoxAdapter(
      child: GallerySelectionBar(
        controller: _controller,
        onSelectAll: _controller.selectAll,
        onClearSelection: _controller.clearSelection,
        onDelete: () => _showDeleteConfirmation(context),
      ),
    );
  }

  Widget _buildContent() {
    if (_controller.isLoading) {
      return const SliverFillRemaining(
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (_controller.error != null) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                _controller.error!,
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _controller.refresh,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (!_controller.hasItems) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.photo_library_outlined,
                size: 64,
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'No items in gallery',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'Create your first collage or import some photos',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SliverToBoxAdapter(
      child: GalleryGrid(
        controller: _controller,
        items: _controller.displayItems,
        onItemTap: _handleItemTap,
        onItemLongPress: _handleItemLongPress,
      ),
    );
  }

  Widget? _buildFloatingActionButton(BuildContext context) {
    if (_controller.isSelectionMode) return null;

    return FloatingActionButton(
      onPressed: () {
        // TODO: Show import options
      },
      backgroundColor: Theme.of(context).colorScheme.primary,
      child: const Icon(Icons.add),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'sort':
        _showSortOptions(context);
        break;
      case 'select':
        _controller.toggleSelectionMode();
        break;
      case 'refresh':
        _controller.refresh();
        break;
    }
  }

  void _handleItemTap(GalleryItem item) {
    if (_controller.isSelectionMode) {
      _controller.toggleItemSelection(item.id);
    } else {
      // TODO: Open item viewer
    }
  }

  void _handleItemLongPress(GalleryItem item) {
    HapticFeedback.mediumImpact();
    if (!_controller.isSelectionMode) {
      _controller.toggleSelectionMode();
    }
    _controller.toggleItemSelection(item.id);
  }

  void _showSortOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Sort by', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 16),
            ...GallerySortBy.values.map(
              (sortBy) => ListTile(
                title: Text(_getSortByName(sortBy)),
                trailing: _controller.sortBy == sortBy
                    ? Icon(
                        _controller.sortOrder == GallerySortOrder.ascending
                            ? Icons.arrow_upward
                            : Icons.arrow_downward,
                      )
                    : null,
                onTap: () {
                  _controller.setSortBy(sortBy);
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Items'),
        content: Text(
          'Are you sure you want to delete ${_controller.selectedCount} item(s)? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _controller.deleteSelectedItems();
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _getSortByName(GallerySortBy sortBy) {
    switch (sortBy) {
      case GallerySortBy.dateCreated:
        return 'Date Created';
      case GallerySortBy.dateModified:
        return 'Date Modified';
      case GallerySortBy.name:
        return 'Name';
      case GallerySortBy.size:
        return 'Size';
      case GallerySortBy.type:
        return 'Type';
    }
  }
}
