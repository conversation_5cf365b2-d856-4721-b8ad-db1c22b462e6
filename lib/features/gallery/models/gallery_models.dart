import 'package:flutter/foundation.dart';

/// Gallery item types
enum GalleryItemType {
  image,
  collage,
  livePhoto,
  video,
}

/// Gallery filter options
enum GalleryFilter {
  all,
  images,
  collages,
  livePhotos,
  videos,
  recent,
  favorites,
}

/// Gallery sort options
enum GallerySortBy {
  dateCreated,
  dateModified,
  name,
  size,
  type,
}

/// Gallery sort order
enum GallerySortOrder {
  ascending,
  descending,
}

/// Gallery view mode
enum GalleryViewMode {
  grid,
  list,
  masonry,
}

/// Gallery item model
class GalleryItem {
  final String id;
  final String name;
  final String path;
  final GalleryItemType type;
  final DateTime createdAt;
  final DateTime modifiedAt;
  final int size;
  final String? thumbnailPath;
  final Map<String, dynamic>? metadata;
  final bool isFavorite;
  final List<String> tags;

  const GalleryItem({
    required this.id,
    required this.name,
    required this.path,
    required this.type,
    required this.createdAt,
    required this.modifiedAt,
    required this.size,
    this.thumbnailPath,
    this.metadata,
    this.isFavorite = false,
    this.tags = const [],
  });

  /// Create a copy with updated properties
  GalleryItem copyWith({
    String? id,
    String? name,
    String? path,
    GalleryItemType? type,
    DateTime? createdAt,
    DateTime? modifiedAt,
    int? size,
    String? thumbnailPath,
    Map<String, dynamic>? metadata,
    bool? isFavorite,
    List<String>? tags,
  }) {
    return GalleryItem(
      id: id ?? this.id,
      name: name ?? this.name,
      path: path ?? this.path,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      size: size ?? this.size,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      metadata: metadata ?? this.metadata,
      isFavorite: isFavorite ?? this.isFavorite,
      tags: tags ?? this.tags,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'path': path,
      'type': type.name,
      'createdAt': createdAt.toIso8601String(),
      'modifiedAt': modifiedAt.toIso8601String(),
      'size': size,
      'thumbnailPath': thumbnailPath,
      'metadata': metadata,
      'isFavorite': isFavorite,
      'tags': tags,
    };
  }

  /// Create from JSON
  factory GalleryItem.fromJson(Map<String, dynamic> json) {
    return GalleryItem(
      id: json['id'],
      name: json['name'],
      path: json['path'],
      type: GalleryItemType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => GalleryItemType.image,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      modifiedAt: DateTime.parse(json['modifiedAt']),
      size: json['size'],
      thumbnailPath: json['thumbnailPath'],
      metadata: json['metadata'],
      isFavorite: json['isFavorite'] ?? false,
      tags: List<String>.from(json['tags'] ?? []),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GalleryItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'GalleryItem(id: $id, name: $name, type: $type)';
  }
}

/// Gallery selection state
class GallerySelection {
  final Set<String> selectedIds;
  final bool isSelectionMode;

  const GallerySelection({
    this.selectedIds = const {},
    this.isSelectionMode = false,
  });

  /// Check if an item is selected
  bool isSelected(String id) => selectedIds.contains(id);

  /// Get selected count
  int get selectedCount => selectedIds.length;

  /// Check if all items are selected
  bool isAllSelected(List<GalleryItem> items) {
    return items.isNotEmpty && selectedIds.length == items.length;
  }

  /// Create a copy with updated selection
  GallerySelection copyWith({
    Set<String>? selectedIds,
    bool? isSelectionMode,
  }) {
    return GallerySelection(
      selectedIds: selectedIds ?? this.selectedIds,
      isSelectionMode: isSelectionMode ?? this.isSelectionMode,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GallerySelection &&
        setEquals(other.selectedIds, selectedIds) &&
        other.isSelectionMode == isSelectionMode;
  }

  @override
  int get hashCode => Object.hash(selectedIds, isSelectionMode);
}

/// Gallery search state
class GallerySearchState {
  final String query;
  final bool isSearching;
  final List<GalleryItem> searchResults;

  const GallerySearchState({
    this.query = '',
    this.isSearching = false,
    this.searchResults = const [],
  });

  /// Check if search is active
  bool get isActive => query.isNotEmpty;

  /// Create a copy with updated search state
  GallerySearchState copyWith({
    String? query,
    bool? isSearching,
    List<GalleryItem>? searchResults,
  }) {
    return GallerySearchState(
      query: query ?? this.query,
      isSearching: isSearching ?? this.isSearching,
      searchResults: searchResults ?? this.searchResults,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GallerySearchState &&
        other.query == query &&
        other.isSearching == isSearching &&
        listEquals(other.searchResults, searchResults);
  }

  @override
  int get hashCode => Object.hash(query, isSearching, searchResults);
}
