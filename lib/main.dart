import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'app/app.dart';
import 'app/router.dart';
import 'app/theme.dart';
import 'core/services/service_locator.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  await setupServices();

  runApp(const TuShenApp());
}

class TuShenApp extends StatelessWidget {
  const TuShenApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'TuShen',

      // Theme configuration
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,

      // Internationalization
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      supportedLocales: const [
        Locale('en'), // English
        Locale('zh'), // 简体中文
        Locale('zh', 'TW'), // 繁体中文
        Locale('ja'), // 日本語
        Locale('ko'), // 한국어
      ],

      // Router configuration
      routerConfig: appRouter,

      // Debug configuration
      debugShowCheckedModeBanner: false,
    );
  }
}
