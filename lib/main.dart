import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'l10n/generated/app_localizations.dart';

import 'app/app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  await setupServices();

  runApp(const TuShenApp());
}

class TuShenApp extends StatelessWidget {
  const TuShenApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'TuShen',

      // Theme configuration
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,

      // Internationalization
      localizationsDelegates: [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      supportedLocales: const [
        Locale('en'), // English
        Locale('zh'), // 简体中文
        Locale('zh', 'TW'), // 繁体中文
        Locale('ja'), // 日本語
        Locale('ko'), // 한국어
      ],

      // Router configuration
      routerConfig: appRouter,

      // Debug configuration
      debugShowCheckedModeBanner: false,
    );
  }
}
