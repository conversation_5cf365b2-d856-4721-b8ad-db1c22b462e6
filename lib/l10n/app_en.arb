{"@@locale": "en", "appTitle": "<PERSON><PERSON><PERSON>", "@appTitle": {"description": "The title of the application"}, "welcome": "Welcome to TuShen", "@welcome": {"description": "Welcome message"}, "welcomeSubtitle": "Create stunning collages and edit your photos with professional tools", "@welcomeSubtitle": {"description": "Welcome subtitle message"}, "createCollage": "Create Collage", "@createCollage": {"description": "Button text for creating a collage"}, "createCollageSubtitle": "Combine multiple photos", "@createCollageSubtitle": {"description": "Subtitle for create collage action"}, "editPhoto": "Edit Photo", "@editPhoto": {"description": "Button text for editing a photo"}, "editPhotoSubtitle": "Apply filters & effects", "@editPhotoSubtitle": {"description": "Subtitle for edit photo action"}, "gallery": "Gallery", "@gallery": {"description": "Gallery tab label"}, "settings": "Settings", "@settings": {"description": "Settings tab label"}, "home": "Home", "@home": {"description": "Home tab label"}, "quickActions": "Quick Actions", "@quickActions": {"description": "Quick actions section title"}, "recentWork": "Recent Work", "@recentWork": {"description": "Recent work section title"}, "viewAll": "View All", "@viewAll": {"description": "View all button text"}, "noRecentImages": "No recent images", "@noRecentImages": {"description": "Message when no recent images are available"}, "selectImage": "Select Image", "@selectImage": {"description": "Select image dialog title"}, "selectImageDescription": "Choose how you want to add an image", "@selectImageDescription": {"description": "Select image dialog description"}, "takePhoto": "Take Photo", "@takePhoto": {"description": "Take photo option"}, "takePhotoDescription": "Use camera to capture a new photo", "@takePhotoDescription": {"description": "Take photo option description"}, "chooseFromGallery": "Choose from Gallery", "@chooseFromGallery": {"description": "Choose from gallery option"}, "chooseFromGalleryDescription": "Select an existing photo from your gallery", "@chooseFromGalleryDescription": {"description": "Choose from gallery option description"}, "selectMultiple": "Select Multiple", "@selectMultiple": {"description": "Select multiple images option"}, "selectMultipleDescription": "Choose multiple photos for collage", "@selectMultipleDescription": {"description": "Select multiple images option description"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "loading": "Loading...", "@loading": {"description": "Loading message"}, "error": "Error", "@error": {"description": "Error message"}, "permissionDenied": "Permission denied", "@permissionDenied": {"description": "Permission denied message"}, "cameraPermissionDenied": "Camera permission denied", "@cameraPermissionDenied": {"description": "Camera permission denied message"}, "photoLibraryPermissionDenied": "Photo library permission denied", "@photoLibraryPermissionDenied": {"description": "Photo library permission denied message"}, "failedToLoadImages": "Failed to load images", "@failedToLoadImages": {"description": "Failed to load images error message"}, "failedToPickImage": "Failed to pick image", "@failedToPickImage": {"description": "Failed to pick image error message"}, "features": "Features", "@features": {"description": "Features section title"}, "professionalTools": "Professional Tools", "@professionalTools": {"description": "Professional tools feature title"}, "professionalToolsDescription": "Use professional-grade image processing tools", "@professionalToolsDescription": {"description": "Professional tools feature description"}, "highPerformance": "High Performance", "@highPerformance": {"description": "High performance feature title"}, "highPerformanceDescription": "Rust-based high-speed image processing engine", "@highPerformanceDescription": {"description": "High performance feature description"}, "crossPlatform": "Cross Platform", "@crossPlatform": {"description": "Cross platform feature title"}, "crossPlatformDescription": "Support for iOS, Android, macOS, Windows", "@crossPlatformDescription": {"description": "Cross platform feature description"}}