// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'TuShen';

  @override
  String get welcome => 'Welcome to TuShen';

  @override
  String get createCollage => 'Create Collage';

  @override
  String get editPhoto => 'Edit Photo';

  @override
  String get gallery => 'Gallery';

  @override
  String get settings => 'Settings';

  @override
  String get home => 'Home';
}
