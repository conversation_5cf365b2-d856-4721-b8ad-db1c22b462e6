// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'TuShen';

  @override
  String get welcome => 'Welcome to TuShen';

  @override
  String get welcomeSubtitle =>
      'Create stunning collages and edit your photos with professional tools';

  @override
  String get createCollage => 'Create Collage';

  @override
  String get createCollageSubtitle => 'Combine multiple photos';

  @override
  String get editPhoto => 'Edit Photo';

  @override
  String get editPhotoSubtitle => 'Apply filters & effects';

  @override
  String get gallery => 'Gallery';

  @override
  String get settings => 'Settings';

  @override
  String get home => 'Home';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get recentWork => 'Recent Work';

  @override
  String get viewAll => 'View All';

  @override
  String get noRecentImages => 'No recent images';

  @override
  String get selectImage => 'Select Image';

  @override
  String get selectImageDescription => 'Choose how you want to add an image';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get takePhotoDescription => 'Use camera to capture a new photo';

  @override
  String get chooseFromGallery => 'Choose from Gallery';

  @override
  String get chooseFromGalleryDescription =>
      'Select an existing photo from your gallery';

  @override
  String get selectMultiple => 'Select Multiple';

  @override
  String get selectMultipleDescription => 'Choose multiple photos for collage';

  @override
  String get cancel => 'Cancel';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get permissionDenied => 'Permission denied';

  @override
  String get cameraPermissionDenied => 'Camera permission denied';

  @override
  String get photoLibraryPermissionDenied => 'Photo library permission denied';

  @override
  String get failedToLoadImages => 'Failed to load images';

  @override
  String get failedToPickImage => 'Failed to pick image';

  @override
  String get features => 'Features';

  @override
  String get professionalTools => 'Professional Tools';

  @override
  String get professionalToolsDescription =>
      'Use professional-grade image processing tools';

  @override
  String get highPerformance => 'High Performance';

  @override
  String get highPerformanceDescription =>
      'Rust-based high-speed image processing engine';

  @override
  String get crossPlatform => 'Cross Platform';

  @override
  String get crossPlatformDescription =>
      'Support for iOS, Android, macOS, Windows';
}
