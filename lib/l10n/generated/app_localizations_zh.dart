// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => '图神';

  @override
  String get welcome => '欢迎使用图神';

  @override
  String get createCollage => '创建拼贴';

  @override
  String get editPhoto => '编辑照片';

  @override
  String get gallery => '图库';

  @override
  String get settings => '设置';

  @override
  String get home => '首页';
}
