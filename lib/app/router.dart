import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../features/home/<USER>';
import '../features/collage/collage_screen.dart';
import '../features/editor/editor_screen.dart';
import '../features/gallery/gallery_screen.dart';
import '../features/settings/settings_screen.dart';
import '../features/onboarding/onboarding_screen.dart';
import '../features/splash/splash_screen.dart';
import '../features/debug/ffi_test_page.dart';
import '../features/live_photo/live_photo_screen.dart';
import '../features/format_converter/format_converter_screen.dart';
import '../core/services/service_locator.dart';
import '../core/services/storage_service.dart';

/// App Router Configuration
///
/// Manages navigation throughout the TuShen app using GoRouter.
/// Provides type-safe navigation with support for nested routes,
/// guards, and transitions.

// Route paths
class AppRoutes {
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String home = '/home';
  static const String gallery = '/gallery';
  static const String collage = '/collage';
  static const String editor = '/editor';
  static const String settings = '/settings';
  static const String ffiTest = '/ffi-test';
}

// Global router instance
final GoRouter appRouter = GoRouter(
  initialLocation: AppRoutes.splash,
  debugLogDiagnostics: true,
  routes: [
    // Splash Screen
    GoRoute(
      path: AppRoutes.splash,
      name: 'splash',
      builder: (context, state) => const SplashScreen(),
    ),

    // Onboarding Screen
    GoRoute(
      path: AppRoutes.onboarding,
      name: 'onboarding',
      builder: (context, state) => const OnboardingScreen(),
    ),

    // Main App Shell with Bottom Navigation
    ShellRoute(
      builder: (context, state, child) => MainShell(child: child),
      routes: [
        // Home Tab
        GoRoute(
          path: AppRoutes.home,
          name: 'home',
          builder: (context, state) => const HomeScreen(),
        ),

        // Gallery Tab
        GoRoute(
          path: AppRoutes.gallery,
          name: 'gallery',
          builder: (context, state) => const GalleryScreen(),
        ),

        // Settings Tab
        GoRoute(
          path: AppRoutes.settings,
          name: 'settings',
          builder: (context, state) => const SettingsScreen(),
        ),
      ],
    ),

    // Full Screen Routes (outside main shell)
    GoRoute(
      path: AppRoutes.collage,
      name: 'collage',
      builder: (context, state) {
        final imageIds = state.uri.queryParameters['images']?.split(',') ?? [];
        return CollageScreen(imageIds: imageIds);
      },
    ),

    GoRoute(
      path: AppRoutes.editor,
      name: 'editor',
      builder: (context, state) {
        final imageId = state.uri.queryParameters['imageId'] ?? '';
        return EditorScreen(imageId: imageId);
      },
    ),

    GoRoute(
      path: '/live-photo',
      name: 'livePhoto',
      builder: (context, state) {
        final imageId = state.uri.queryParameters['imageId'];
        final videoId = state.uri.queryParameters['videoId'];
        return LivePhotoScreen(imageId: imageId, videoId: videoId);
      },
    ),

    GoRoute(
      path: '/format-converter',
      name: 'formatConverter',
      builder: (context, state) {
        final imageId = state.uri.queryParameters['imageId'];
        return FormatConverterScreen(imageId: imageId);
      },
    ),

    // Debug Routes
    GoRoute(
      path: AppRoutes.ffiTest,
      name: 'ffiTest',
      builder: (context, state) => const FFITestPage(),
    ),
  ],

  // Redirect logic for authentication and onboarding
  redirect: (context, state) async {
    final storageService = getIt<StorageService>();

    // Check if user has completed onboarding
    final hasCompletedOnboarding =
        storageService.getBool('onboarding_completed') ?? false;

    // If on splash screen, determine where to redirect
    if (state.matchedLocation == AppRoutes.splash) {
      // Add a small delay for splash screen
      await Future.delayed(const Duration(milliseconds: 1500));

      if (!hasCompletedOnboarding) {
        return AppRoutes.onboarding;
      } else {
        return AppRoutes.home;
      }
    }

    // If trying to access main app without completing onboarding
    if (!hasCompletedOnboarding &&
        state.matchedLocation != AppRoutes.onboarding &&
        state.matchedLocation != AppRoutes.splash) {
      return AppRoutes.onboarding;
    }

    return null; // No redirect needed
  },

  // Error handling
  errorBuilder: (context, state) => Scaffold(
    appBar: AppBar(title: const Text('Page Not Found')),
    body: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Page not found: ${state.matchedLocation}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => context.go(AppRoutes.home),
            child: const Text('Go Home'),
          ),
        ],
      ),
    ),
  ),
);

/// Main Shell Widget with Bottom Navigation
///
/// Provides the main app structure with bottom navigation bar
/// and handles tab switching logic.
class MainShell extends StatefulWidget {
  final Widget child;

  const MainShell({super.key, required this.child});

  @override
  State<MainShell> createState() => _MainShellState();
}

class _MainShellState extends State<MainShell> {
  int _currentIndex = 0;

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });

    switch (index) {
      case 0:
        context.go(AppRoutes.home);
        break;
      case 1:
        context.go(AppRoutes.gallery);
        break;
      case 2:
        context.go(AppRoutes.settings);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Update current index based on current route
    final location = GoRouterState.of(context).matchedLocation;
    switch (location) {
      case AppRoutes.home:
        _currentIndex = 0;
        break;
      case AppRoutes.gallery:
        _currentIndex = 1;
        break;
      case AppRoutes.settings:
        _currentIndex = 2;
        break;
    }

    return Scaffold(
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.photo_library_outlined),
            activeIcon: Icon(Icons.photo_library),
            label: 'Gallery',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings_outlined),
            activeIcon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}

/// Router Extensions for Type-Safe Navigation
extension AppRouterExtension on BuildContext {
  /// Navigate to home screen
  void goHome() => go(AppRoutes.home);

  /// Navigate to gallery screen
  void goGallery() => go(AppRoutes.gallery);

  /// Navigate to settings screen
  void goSettings() => go(AppRoutes.settings);

  /// Navigate to collage screen with image IDs
  void goCollage(List<String> imageIds) {
    final query = imageIds.isNotEmpty ? '?images=${imageIds.join(',')}' : '';
    go('${AppRoutes.collage}$query');
  }

  /// Navigate to editor screen with image ID
  void goEditor(String imageId) {
    go('${AppRoutes.editor}?imageId=$imageId');
  }

  /// Navigate to live photo screen with image ID
  void goLivePhoto(String imageId) {
    go('/live-photo?imageId=$imageId');
  }

  /// Navigate to format converter screen with image ID
  void goFormatConverter(String imageId) {
    go('/format-converter?imageId=$imageId');
  }

  /// Navigate to onboarding screen
  void goOnboarding() => go(AppRoutes.onboarding);

  /// Navigate to FFI test page
  void goFFITest() => go(AppRoutes.ffiTest);

  /// Check if currently on a specific route
  bool isCurrentRoute(String route) {
    return GoRouterState.of(this).matchedLocation == route;
  }
}

/// Custom Page Transitions
class SlideTransitionPage extends CustomTransitionPage<void> {
  const SlideTransitionPage({
    required super.child,
    required super.name,
    super.arguments,
    super.restorationId,
    super.key,
  }) : super(
         transitionsBuilder: _slideTransitionsBuilder,
         transitionDuration: const Duration(milliseconds: 300),
       );

  static Widget _slideTransitionsBuilder(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return SlideTransition(
      position: animation.drive(
        Tween<Offset>(
          begin: const Offset(1.0, 0.0),
          end: Offset.zero,
        ).chain(CurveTween(curve: Curves.easeInOut)),
      ),
      child: child,
    );
  }
}

/// Fade Transition Page
class FadeTransitionPage extends CustomTransitionPage<void> {
  const FadeTransitionPage({
    required super.child,
    required super.name,
    super.arguments,
    super.restorationId,
    super.key,
  }) : super(
         transitionsBuilder: _fadeTransitionsBuilder,
         transitionDuration: const Duration(milliseconds: 200),
       );

  static Widget _fadeTransitionsBuilder(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return FadeTransition(
      opacity: animation.drive(CurveTween(curve: Curves.easeInOut)),
      child: child,
    );
  }
}
