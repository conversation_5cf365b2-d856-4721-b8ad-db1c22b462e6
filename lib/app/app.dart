/// TuShen App - Core application exports
///
/// This file serves as the main entry point for the TuShen application,
/// providing centralized access to all core app functionality.
library;

// Core app components
export 'router.dart';
export 'theme.dart';

// Controllers (State Management)
export '../core/controllers/app_controller.dart';
export '../core/controllers/image_controller.dart';
export '../core/controllers/collage_controller.dart';
export '../core/controllers/settings_controller.dart';

// Services
export '../core/services/service_locator.dart';
export '../core/services/image_service.dart';
export '../core/services/storage_service.dart';
export '../core/services/permission_service.dart';

// Features
export '../features/home/<USER>';
export '../features/gallery/gallery_screen.dart';
export '../features/collage/collage_screen.dart';
export '../features/editor/editor_screen.dart';
export '../features/settings/settings_screen.dart';
export '../features/onboarding/onboarding_screen.dart';
export '../features/splash/splash_screen.dart';
