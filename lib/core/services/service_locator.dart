import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'storage_service.dart';
import 'image_service.dart';
import 'permission_service.dart';
import 'rust_bridge_service.dart';
import 'image_processor_service.dart';
import 'collage_service.dart';
import 'gallery_service.dart';
import '../controllers/app_controller.dart';
import '../controllers/image_controller.dart';
import '../controllers/collage_controller.dart';
import '../controllers/settings_controller.dart';
import '../../features/gallery/controllers/gallery_controller.dart';
import '../../features/editor/controllers/image_editor_controller.dart';
import '../../features/format_converter/controllers/format_converter_controller.dart';
import '../../features/live_photo/controllers/live_photo_controller.dart';
import '../../features/long_screenshot/controllers/long_screenshot_controller.dart';

/// Service Locator for Dependency Injection
///
/// Provides a centralized way to register and access services throughout
/// the app. Uses GetIt for service location and dependency injection.

final GetIt getIt = GetIt.instance;

/// Setup all services and controllers
///
/// This function should be called once during app initialization
/// to register all dependencies.
Future<void> setupServices() async {
  // Register external dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);

  // Register core services
  getIt.registerLazySingleton<StorageService>(
    () => StorageService(getIt<SharedPreferences>()),
  );

  getIt.registerLazySingleton<PermissionService>(() => PermissionService());

  getIt.registerLazySingleton<ImageService>(() => ImageService());

  // Register Rust-based services
  getIt.registerLazySingleton<RustBridgeService>(
    () => RustBridgeService.instance,
  );

  getIt.registerLazySingleton<ImageProcessorService>(
    () => ImageProcessorService.instance,
  );

  getIt.registerLazySingleton<CollageService>(() => CollageService.instance);

  getIt.registerLazySingleton<GalleryService>(() => GalleryService.instance);

  // Register controllers
  getIt.registerLazySingleton<AppController>(
    () => AppController(storageService: getIt<StorageService>()),
  );

  getIt.registerLazySingleton<SettingsController>(
    () => SettingsController(storageService: getIt<StorageService>()),
  );

  getIt.registerLazySingleton<ImageController>(
    () => ImageController(
      imageService: getIt<ImageService>(),
      permissionService: getIt<PermissionService>(),
    ),
  );

  getIt.registerLazySingleton<CollageController>(
    () => CollageController(
      imageService: getIt<ImageService>(),
      galleryService: getIt<GalleryService>(),
    ),
  );

  getIt.registerLazySingleton<GalleryController>(() => GalleryController());

  getIt.registerLazySingleton<ImageEditorController>(
    () => ImageEditorController(getIt<ImageService>(), getIt<GalleryService>()),
  );

  getIt.registerLazySingleton<FormatConverterController>(
    () => FormatConverterController(
      getIt<ImageService>(),
      getIt<GalleryService>(),
    ),
  );

  getIt.registerLazySingleton<LivePhotoController>(
    () => LivePhotoController(getIt<ImageService>(), getIt<GalleryService>()),
  );

  getIt.registerLazySingleton<LongScreenshotController>(
    () => LongScreenshotController(
      getIt<ImageService>(),
      getIt<GalleryService>(),
    ),
  );

  // Initialize services that need setup
  // TODO: Fix Rust bridge library loading
  // await getIt<RustBridgeService>().initialize();
  await getIt<PermissionService>().initialize();
  await getIt<ImageService>().initialize();
  await getIt<ImageProcessorService>().initialize();
  await getIt<CollageService>().initialize();
  await getIt<AppController>().initialize();
  await getIt<SettingsController>().initialize();
}

/// Cleanup all services
///
/// This function should be called when the app is being disposed
/// to properly cleanup resources.
Future<void> cleanupServices() async {
  await getIt<ImageService>().dispose();
  await getIt<AppController>().dispose();
  await getIt<SettingsController>().dispose();
  await getIt<ImageController>().dispose();
  await getIt<CollageController>().dispose();

  await getIt.reset();
}

/// Service Locator Extensions
///
/// Provides convenient access to commonly used services
extension ServiceLocatorExtensions on GetIt {
  /// Get the storage service
  StorageService get storage => get<StorageService>();

  /// Get the image service
  ImageService get imageService => get<ImageService>();

  /// Get the permission service
  PermissionService get permissions => get<PermissionService>();

  /// Get the app controller
  AppController get appController => get<AppController>();

  /// Get the settings controller
  SettingsController get settingsController => get<SettingsController>();

  /// Get the image controller
  ImageController get imageController => get<ImageController>();

  /// Get the collage controller
  CollageController get collageController => get<CollageController>();

  /// Get the Rust bridge service
  RustBridgeService get rustBridge => get<RustBridgeService>();

  /// Get the image processor service
  ImageProcessorService get imageProcessor => get<ImageProcessorService>();

  /// Get the collage service
  CollageService get collageService => get<CollageService>();
}
