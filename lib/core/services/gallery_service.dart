import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../../features/gallery/models/gallery_models.dart';
import '../../features/gallery/controllers/gallery_controller.dart';
import 'service_locator.dart';

/// Service for managing gallery operations across the app
class GalleryService {
  static GalleryService? _instance;
  static GalleryService get instance => _instance ??= GalleryService._();
  GalleryService._();

  /// Save an image to the gallery from various sources
  Future<bool> saveImageToGallery({
    required String sourcePath,
    required String name,
    required GalleryItemType type,
    List<String> tags = const [],
    String? customDirectory,
  }) async {
    try {
      // Get the app's documents directory
      final appDir = await getApplicationDocumentsDirectory();
      final galleryDir = Directory(path.join(appDir.path, 'TuShen', 'Gallery'));

      // Create gallery directory if it doesn't exist
      if (!await galleryDir.exists()) {
        await galleryDir.create(recursive: true);
      }

      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(sourcePath);
      final fileName = '${name}_$timestamp$extension';
      final destinationPath = path.join(galleryDir.path, fileName);

      // Copy the file to gallery directory
      final sourceFile = File(sourcePath);
      if (!await sourceFile.exists()) {
        throw Exception('Source file does not exist: $sourcePath');
      }

      final destinationFile = await sourceFile.copy(destinationPath);

      // Get file stats
      final stat = await destinationFile.stat();

      // Create gallery item
      final galleryItem = GalleryItem(
        id: timestamp.toString(),
        name: name,
        path: destinationPath,
        type: type,
        createdAt: DateTime.now(),
        modifiedAt: DateTime.now(),
        size: stat.size,
        tags: tags,
      );

      // Add to gallery controller
      final galleryController = getIt<GalleryController>();
      await galleryController.addItem(galleryItem);

      return true;
    } catch (e) {
      print('Failed to save image to gallery: $e');
      return false;
    }
  }

  /// Save collage to gallery
  Future<bool> saveCollageToGallery({
    required String collagePath,
    required String name,
    List<String> sourceImagePaths = const [],
  }) async {
    final tags = ['collage'];
    if (sourceImagePaths.isNotEmpty) {
      tags.add('${sourceImagePaths.length} images');
    }

    return await saveImageToGallery(
      sourcePath: collagePath,
      name: name,
      type: GalleryItemType.collage,
      tags: tags,
    );
  }

  /// Save edited image to gallery
  Future<bool> saveEditedImageToGallery({
    required String editedImagePath,
    required String originalName,
    List<String> appliedFilters = const [],
  }) async {
    final tags = ['edited'];
    if (appliedFilters.isNotEmpty) {
      tags.addAll(appliedFilters);
    }

    return await saveImageToGallery(
      sourcePath: editedImagePath,
      name: '${originalName}_edited',
      type: GalleryItemType.image,
      tags: tags,
    );
  }

  /// Save format converted image to gallery
  Future<bool> saveConvertedImageToGallery({
    required String convertedImagePath,
    required String originalName,
    required String fromFormat,
    required String toFormat,
  }) async {
    return await saveImageToGallery(
      sourcePath: convertedImagePath,
      name: '${originalName}_$fromFormat-to-$toFormat',
      type: GalleryItemType.image,
      tags: ['converted', fromFormat, toFormat],
    );
  }

  /// Save live photo to gallery
  Future<bool> saveLivePhotoToGallery({
    required String livePhotoPath,
    required String name,
    String? prompt,
  }) async {
    final tags = ['live-photo', 'generated'];
    if (prompt != null && prompt.isNotEmpty) {
      tags.add('prompt: $prompt');
    }

    return await saveImageToGallery(
      sourcePath: livePhotoPath,
      name: name,
      type: GalleryItemType.livePhoto,
      tags: tags,
    );
  }

  /// Save long screenshot to gallery
  Future<bool> saveLongScreenshotToGallery({
    required String screenshotPath,
    required String name,
    required int sourceCount,
  }) async {
    final tags = ['long-screenshot', 'stitched', 'sources:$sourceCount'];

    return await saveImageToGallery(
      sourcePath: screenshotPath,
      name: name,
      type: GalleryItemType.longScreenshot,
      tags: tags,
    );
  }

  /// Get gallery directory path
  Future<String> getGalleryDirectoryPath() async {
    final appDir = await getApplicationDocumentsDirectory();
    return path.join(appDir.path, 'TuShen', 'Gallery');
  }

  /// Clean up temporary files (call this periodically)
  Future<void> cleanupTempFiles() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final tempDir = Directory(path.join(appDir.path, 'TuShen', 'temp'));

      if (await tempDir.exists()) {
        final files = await tempDir.list().toList();
        final now = DateTime.now();

        for (final file in files) {
          if (file is File) {
            final stat = await file.stat();
            final age = now.difference(stat.modified);

            // Delete files older than 24 hours
            if (age.inHours > 24) {
              try {
                await file.delete();
              } catch (e) {
                // Ignore deletion errors
              }
            }
          }
        }
      }
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}
