import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:image_picker/image_picker.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// Image Service
///
/// Handles image loading, processing, and management operations.
/// Provides interface to the Rust core library for high-performance processing.
class ImageService {
  final ImagePicker _picker = ImagePicker();

  /// Initialize the image service
  Future<void> initialize() async {
    // TODO: Initialize Rust core library
    // await TuShenCore.initialize();
  }

  /// Dispose the image service
  Future<void> dispose() async {
    // TODO: Cleanup Rust core library
    // await TuShenCore.cleanup();
  }

  /// Pick image from camera
  Future<ImageData?> pickImageFromCamera() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (image != null) {
        return await _createImageDataFromFile(image);
      }
      return null;
    } catch (e) {
      debugPrint('Error picking image from camera: $e');
      return null;
    }
  }

  /// Pick image from gallery
  Future<ImageData?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        return await _createImageDataFromFile(image);
      }
      return null;
    } catch (e) {
      debugPrint('Error picking image from gallery: $e');
      return null;
    }
  }

  /// Pick multiple images from gallery
  Future<List<ImageData>> pickMultipleImages() async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(imageQuality: 85);

      List<ImageData> imageDataList = [];
      for (XFile image in images) {
        final imageData = await _createImageDataFromFile(image);
        if (imageData != null) {
          imageDataList.add(imageData);
        }
      }
      return imageDataList;
    } catch (e) {
      debugPrint('Error picking multiple images: $e');
      return [];
    }
  }

  /// Load recent images from photo library
  Future<List<ImageData>> loadRecentImages({int count = 20}) async {
    try {
      final PermissionState ps = await PhotoManager.requestPermissionExtend();
      if (!ps.hasAccess) {
        debugPrint('Photo permission denied');
        return [];
      }

      final List<AssetPathEntity> paths = await PhotoManager.getAssetPathList(
        type: RequestType.image,
      );

      if (paths.isEmpty) return [];

      final List<AssetEntity> assets = await paths.first.getAssetListPaged(
        page: 0,
        size: count,
      );

      List<ImageData> imageDataList = [];
      for (AssetEntity asset in assets) {
        final imageData = await _createImageDataFromAsset(asset);
        if (imageData != null) {
          imageDataList.add(imageData);
        }
      }

      return imageDataList;
    } catch (e) {
      debugPrint('Error loading recent images: $e');
      return [];
    }
  }

  /// Load image from file path
  Future<ImageData?> loadImage(String path) async {
    try {
      final File file = File(path);
      if (!await file.exists()) return null;

      final Uint8List bytes = await file.readAsBytes();

      // Use a simple approach to get image dimensions
      // In a real app, you might want to use a proper image library
      int width = 1920; // Default width
      int height = 1080; // Default height

      try {
        final ui.Codec codec = await ui.instantiateImageCodec(bytes);
        final ui.FrameInfo frameInfo = await codec.getNextFrame();
        width = frameInfo.image.width;
        height = frameInfo.image.height;
        frameInfo.image.dispose();
        codec.dispose();
      } catch (e) {
        debugPrint('Could not decode image dimensions: $e');
      }

      return ImageData(
        path: path,
        width: width,
        height: height,
        bytes: bytes,
        fileName: path.split('/').last,
        fileSize: bytes.length,
      );
    } catch (e) {
      debugPrint('Error loading image from path: $e');
      return null;
    }
  }

  /// Save image to file
  Future<bool> saveImage(ImageData imageData, String path) async {
    try {
      final File file = File(path);
      await file.writeAsBytes(imageData.bytes);
      return true;
    } catch (e) {
      debugPrint('Error saving image: $e');
      return false;
    }
  }

  /// Create ImageData from XFile
  Future<ImageData?> _createImageDataFromFile(XFile file) async {
    try {
      final Uint8List bytes = await file.readAsBytes();

      // Get image dimensions
      int width = 1920; // Default width
      int height = 1080; // Default height

      try {
        final ui.Codec codec = await ui.instantiateImageCodec(bytes);
        final ui.FrameInfo frameInfo = await codec.getNextFrame();
        width = frameInfo.image.width;
        height = frameInfo.image.height;
        frameInfo.image.dispose();
        codec.dispose();
      } catch (e) {
        debugPrint('Could not decode image dimensions: $e');
      }

      return ImageData(
        path: file.path,
        width: width,
        height: height,
        bytes: bytes,
        fileName: file.name,
        fileSize: bytes.length,
      );
    } catch (e) {
      debugPrint('Error creating ImageData from file: $e');
      return null;
    }
  }

  /// Create ImageData from AssetEntity
  Future<ImageData?> _createImageDataFromAsset(AssetEntity asset) async {
    try {
      final File? file = await asset.file;
      if (file == null) return null;

      final Uint8List bytes = await file.readAsBytes();

      return ImageData(
        path: file.path,
        width: asset.width,
        height: asset.height,
        bytes: bytes,
        fileName: asset.title ?? 'image_${asset.id}',
        fileSize: bytes.length,
        assetId: asset.id,
      );
    } catch (e) {
      debugPrint('Error creating ImageData from asset: $e');
      return null;
    }
  }

  /// Process image with filters
  Future<ImageData?> processImage(
    ImageData imageData,
    Map<String, dynamic> options,
  ) async {
    // TODO: Implement image processing with Rust backend
    return imageData;
  }

  /// Create collage from multiple images
  Future<ImageData?> createCollage(
    List<ImageData> images,
    Map<String, dynamic> layout,
  ) async {
    // TODO: Implement collage creation with Rust backend
    if (images.isEmpty) return null;

    // For now, return the first image as a placeholder
    return images.first;
  }
}

/// Enhanced ImageData class
class ImageData {
  final String path;
  final int width;
  final int height;
  final Uint8List bytes;
  final String fileName;
  final int fileSize;
  final String? assetId;

  ImageData({
    required this.path,
    required this.width,
    required this.height,
    required this.bytes,
    required this.fileName,
    required this.fileSize,
    this.assetId,
  });

  /// Get file extension
  String get extension => fileName.split('.').last.toLowerCase();

  /// Check if image is landscape
  bool get isLandscape => width > height;

  /// Check if image is portrait
  bool get isPortrait => height > width;

  /// Check if image is square
  bool get isSquare => width == height;

  /// Get aspect ratio
  double get aspectRatio => width / height;

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    }
    return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}
