import 'package:permission_handler/permission_handler.dart';

/// Permission Service
/// 
/// Handles all permission requests and checks for the app.
/// Provides a unified interface for managing device permissions.
class PermissionService {
  /// Initialize the permission service
  Future<void> initialize() async {
    // Any initialization logic if needed
  }

  /// Request camera permission
  Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  /// Check if camera permission is granted
  Future<bool> hasCameraPermission() async {
    final status = await Permission.camera.status;
    return status.isGranted;
  }

  /// Request photo library permission
  Future<bool> requestPhotoLibraryPermission() async {
    final status = await Permission.photos.request();
    return status.isGranted;
  }

  /// Check if photo library permission is granted
  Future<bool> hasPhotoLibraryPermission() async {
    final status = await Permission.photos.status;
    return status.isGranted;
  }

  /// Request storage permission (Android)
  Future<bool> requestStoragePermission() async {
    final status = await Permission.storage.request();
    return status.isGranted;
  }

  /// Check if storage permission is granted
  Future<bool> hasStoragePermission() async {
    final status = await Permission.storage.status;
    return status.isGranted;
  }

  /// Request all necessary permissions
  Future<Map<Permission, PermissionStatus>> requestAllPermissions() async {
    return await [
      Permission.camera,
      Permission.photos,
      Permission.storage,
    ].request();
  }

  /// Check if all necessary permissions are granted
  Future<bool> hasAllPermissions() async {
    final camera = await hasCameraPermission();
    final photos = await hasPhotoLibraryPermission();
    final storage = await hasStoragePermission();
    
    return camera && photos && storage;
  }
}
