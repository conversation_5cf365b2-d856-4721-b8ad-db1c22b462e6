import 'package:flutter/material.dart';
import '../buttons/tushen_button.dart';
import '../../design_system/theme.dart';

/// Image picker bottom sheet widget
/// 
/// Provides options for selecting images from camera or gallery
class ImagePickerBottomSheet extends StatelessWidget {
  final VoidCallback? onCameraPressed;
  final VoidCallback? onGalleryPressed;
  final VoidCallback? onMultiplePressed;
  final bool showMultipleOption;

  const ImagePickerBottomSheet({
    super.key,
    this.onCameraPressed,
    this.onGalleryPressed,
    this.onMultiplePressed,
    this.showMultipleOption = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Title
          Text(
            'Select Image',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          
          Text(
            'Choose how you want to add an image',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          
          // Camera option
          _PickerOption(
            icon: Icons.camera_alt_outlined,
            title: 'Take Photo',
            subtitle: 'Use camera to capture a new photo',
            onTap: onCameraPressed,
          ),
          const SizedBox(height: 16),
          
          // Gallery option
          _PickerOption(
            icon: Icons.photo_library_outlined,
            title: 'Choose from Gallery',
            subtitle: 'Select an existing photo from your gallery',
            onTap: onGalleryPressed,
          ),
          
          if (showMultipleOption) ...[
            const SizedBox(height: 16),
            // Multiple images option
            _PickerOption(
              icon: Icons.photo_library_outlined,
              title: 'Select Multiple',
              subtitle: 'Choose multiple photos for collage',
              onTap: onMultiplePressed,
            ),
          ],
          
          const SizedBox(height: 24),
          
          // Cancel button
          TuShenButton(
            variant: TuShenButtonVariant.text,
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          
          // Safe area padding
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  /// Show the image picker bottom sheet
  static Future<void> show(
    BuildContext context, {
    VoidCallback? onCameraPressed,
    VoidCallback? onGalleryPressed,
    VoidCallback? onMultiplePressed,
    bool showMultipleOption = true,
  }) {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ImagePickerBottomSheet(
        onCameraPressed: onCameraPressed,
        onGalleryPressed: onGalleryPressed,
        onMultiplePressed: onMultiplePressed,
        showMultipleOption: showMultipleOption,
      ),
    );
  }
}

/// Individual picker option widget
class _PickerOption extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback? onTap;

  const _PickerOption({
    required this.icon,
    required this.title,
    required this.subtitle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          Navigator.of(context).pop();
          onTap?.call();
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
