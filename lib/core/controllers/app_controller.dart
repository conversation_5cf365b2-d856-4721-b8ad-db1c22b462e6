import 'package:flutter/material.dart';
import '../services/storage_service.dart';

/// Main Application Controller
///
/// Manages global app state including theme, language, and initialization.
/// Uses ChangeNotifier for state management without external dependencies.
class AppController extends ChangeNotifier {
  final StorageService _storageService;

  AppController({required StorageService storageService})
    : _storageService = storageService;

  // App state
  bool _isInitialized = false;
  bool _isLoading = false;
  String? _error;

  // Theme state
  ThemeMode _themeMode = ThemeMode.system;

  // Language state
  Locale _locale = const Locale('en');

  // App settings
  bool _autoSaveEnabled = true;
  bool _highQualityProcessing = true;
  bool _gpuAcceleration = true;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;
  String? get error => _error;
  ThemeMode get themeMode => _themeMode;
  Locale get locale => _locale;
  bool get autoSaveEnabled => _autoSaveEnabled;
  bool get highQualityProcessing => _highQualityProcessing;
  bool get gpuAcceleration => _gpuAcceleration;

  /// Initialize the app controller
  Future<void> initialize() async {
    _setLoading(true);
    _clearError();

    try {
      await _loadSettings();
      _isInitialized = true;
    } catch (e) {
      _setError('Failed to initialize app: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load settings from storage
  Future<void> _loadSettings() async {
    // Load theme mode
    final themeModeString = _storageService.getThemeMode();
    _themeMode = _parseThemeMode(themeModeString);

    // Load language
    final languageCode = _storageService.getAppLanguage();
    _locale = _parseLocale(languageCode);

    // Load app settings
    _autoSaveEnabled =
        _storageService.getBool(StorageKeys.autoSaveEnabled) ?? true;
    _highQualityProcessing =
        _storageService.getBool(StorageKeys.highQualityProcessing) ?? true;
    _gpuAcceleration =
        _storageService.getBool(StorageKeys.gpuAcceleration) ?? true;
  }

  /// Set theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;
    await _storageService.setThemeMode(_themeModeToString(mode));
    notifyListeners();
  }

  /// Set app language
  Future<void> setLocale(Locale locale) async {
    if (_locale == locale) return;

    _locale = locale;
    await _storageService.setAppLanguage(locale.languageCode);
    notifyListeners();
  }

  /// Set auto save enabled
  Future<void> setAutoSaveEnabled(bool enabled) async {
    if (_autoSaveEnabled == enabled) return;

    _autoSaveEnabled = enabled;
    await _storageService.setBool(StorageKeys.autoSaveEnabled, enabled);
    notifyListeners();
  }

  /// Set high quality processing
  Future<void> setHighQualityProcessing(bool enabled) async {
    if (_highQualityProcessing == enabled) return;

    _highQualityProcessing = enabled;
    await _storageService.setBool(StorageKeys.highQualityProcessing, enabled);
    notifyListeners();
  }

  /// Set GPU acceleration
  Future<void> setGpuAcceleration(bool enabled) async {
    if (_gpuAcceleration == enabled) return;

    _gpuAcceleration = enabled;
    await _storageService.setBool(StorageKeys.gpuAcceleration, enabled);
    notifyListeners();
  }

  /// Reset all settings to defaults
  Future<void> resetSettings() async {
    _setLoading(true);

    try {
      await _storageService.remove(StorageKeys.themeMode);
      await _storageService.remove(StorageKeys.appLanguage);
      await _storageService.remove(StorageKeys.autoSaveEnabled);
      await _storageService.remove(StorageKeys.highQualityProcessing);
      await _storageService.remove(StorageKeys.gpuAcceleration);

      // Reset to defaults
      _themeMode = ThemeMode.system;
      _locale = const Locale('en');
      _autoSaveEnabled = true;
      _highQualityProcessing = true;
      _gpuAcceleration = true;

      notifyListeners();
    } catch (e) {
      _setError('Failed to reset settings: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get supported locales
  List<Locale> getSupportedLocales() {
    return const [
      Locale('en'), // English
      Locale('zh'), // 简体中文
      Locale('zh', 'TW'), // 繁体中文
      Locale('ja'), // 日本語
      Locale('ko'), // 한국어
    ];
  }

  /// Check if locale is supported
  bool isLocaleSupported(Locale locale) {
    return getSupportedLocales().any(
      (supportedLocale) =>
          supportedLocale.languageCode == locale.languageCode &&
          supportedLocale.countryCode == locale.countryCode,
    );
  }

  /// Get locale display name
  String getLocaleDisplayName(Locale locale) {
    switch (locale.toString()) {
      case 'en':
        return 'English';
      case 'zh':
        return '简体中文';
      case 'zh_TW':
        return '繁體中文';
      case 'ja':
        return '日本語';
      case 'ko':
        return '한국어';
      default:
        return locale.toString();
    }
  }

  /// Dispose resources
  @override
  Future<void> dispose() async {
    // Cleanup any resources if needed
    super.dispose();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading == loading) return;
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    if (_error == error) return;
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _setError(null);
  }

  ThemeMode _parseThemeMode(String mode) {
    switch (mode) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
      default:
        return ThemeMode.system;
    }
  }

  String _themeModeToString(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
        return 'system';
    }
  }

  Locale _parseLocale(String languageCode) {
    switch (languageCode) {
      case 'zh_TW':
        return const Locale('zh', 'TW');
      case 'zh':
        return const Locale('zh');
      case 'ja':
        return const Locale('ja');
      case 'ko':
        return const Locale('ko');
      case 'en':
      default:
        return const Locale('en');
    }
  }
}

/// App Controller Provider Widget
///
/// Provides the AppController to the widget tree using ChangeNotifierProvider pattern
class AppControllerProvider extends StatelessWidget {
  final AppController controller;
  final Widget child;

  const AppControllerProvider({
    super.key,
    required this.controller,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller,
      builder: (context, _) => child,
    );
  }

  /// Get the AppController from the widget tree
  static AppController of(BuildContext context) {
    final provider = context
        .dependOnInheritedWidgetOfExactType<_AppControllerInherited>();
    assert(provider != null, 'AppController not found in widget tree');
    return provider!.controller;
  }
}

class _AppControllerInherited extends InheritedWidget {
  final AppController controller;

  const _AppControllerInherited({
    required this.controller,
    required super.child,
  });

  @override
  bool updateShouldNotify(_AppControllerInherited oldWidget) {
    return controller != oldWidget.controller;
  }
}
