import 'package:flutter/material.dart';
import '../services/image_service.dart';

/// Collage Controller
///
/// Manages collage creation and editing state including layout selection,
/// image arrangement, and styling options.
class CollageController extends ChangeNotifier {
  final ImageService _imageService;

  CollageController({required ImageService imageService})
    : _imageService = imageService;

  // State
  bool _isLoading = false;
  String? _error;
  List<ImageData> _selectedImages = [];
  String _currentLayout = 'grid';
  ImageData? _currentCollage;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<ImageData> get selectedImages => _selectedImages;
  String get currentLayout => _currentLayout;
  ImageData? get currentCollage => _currentCollage;

  /// Add image to collage
  void addImage(ImageData image) {
    if (!_selectedImages.contains(image)) {
      _selectedImages.add(image);
      notifyListeners();
    }
  }

  /// Remove image from collage
  void removeImage(ImageData image) {
    _selectedImages.remove(image);
    notifyListeners();
  }

  /// Clear all selected images
  void clearImages() {
    _selectedImages.clear();
    notifyListeners();
  }

  /// Set collage layout
  void setLayout(String layout) {
    if (_currentLayout != layout) {
      _currentLayout = layout;
      notifyListeners();
    }
  }

  /// Create collage
  Future<void> createCollage() async {
    if (_selectedImages.isEmpty) {
      _setError('No images selected for collage');
      return;
    }

    _setLoading(true);
    _clearError();

    try {
      final layoutOptions = {
        'type': _currentLayout,
        'spacing': 10,
        'background': 'white',
      };

      final collage = await _imageService.createCollage(
        _selectedImages,
        layoutOptions,
      );
      _currentCollage = collage;
      notifyListeners();
    } catch (e) {
      _setError('Failed to create collage: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Save current collage
  Future<bool> saveCollage(String path) async {
    if (_currentCollage == null) {
      _setError('No collage to save');
      return false;
    }

    _setLoading(true);
    _clearError();

    try {
      final success = await _imageService.saveImage(_currentCollage!, path);
      if (!success) {
        throw Exception('Failed to save collage');
      }
      return true;
    } catch (e) {
      _setError('Failed to save collage: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Dispose resources
  @override
  Future<void> dispose() async {
    super.dispose();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading == loading) return;
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    if (_error == error) return;
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _setError(null);
  }
}
