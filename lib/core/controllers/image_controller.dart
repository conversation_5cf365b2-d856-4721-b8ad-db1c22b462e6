import 'package:flutter/material.dart';
import '../services/image_service.dart';
import '../services/permission_service.dart';

/// Image Controller
///
/// Manages image-related state and operations including loading,
/// processing, and editing images.
class ImageController extends ChangeNotifier {
  final ImageService _imageService;
  final PermissionService _permissionService;

  ImageController({
    required ImageService imageService,
    required PermissionService permissionService,
  }) : _imageService = imageService,
       _permissionService = permissionService;

  // State
  bool _isLoading = false;
  String? _error;
  List<ImageData> _images = [];
  ImageData? _currentImage;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<ImageData> get images => _images;
  ImageData? get currentImage => _currentImage;

  /// Load images from gallery
  Future<void> loadImages() async {
    _setLoading(true);
    _clearError();

    try {
      // Check permissions first
      final hasPermission = await _permissionService
          .hasPhotoLibraryPermission();
      if (!hasPermission) {
        final granted = await _permissionService
            .requestPhotoLibraryPermission();
        if (!granted) {
          throw Exception('Photo library permission denied');
        }
      }

      // TODO: Load images from gallery
      _images = [];
      notifyListeners();
    } catch (e) {
      _setError('Failed to load images: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Select an image
  void selectImage(ImageData image) {
    _currentImage = image;
    notifyListeners();
  }

  /// Clear current selection
  void clearSelection() {
    _currentImage = null;
    notifyListeners();
  }

  /// Dispose resources
  @override
  Future<void> dispose() async {
    super.dispose();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading == loading) return;
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    if (_error == error) return;
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _setError(null);
  }
}
