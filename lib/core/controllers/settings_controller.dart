import 'package:flutter/material.dart';
import '../services/storage_service.dart';

/// Settings Controller
///
/// Manages app settings and user preferences including quality settings,
/// processing options, and user interface preferences.
class SettingsController extends ChangeNotifier {
  final StorageService _storageService;

  SettingsController({required StorageService storageService})
    : _storageService = storageService;

  // State
  bool _isLoading = false;
  String? _error;

  // Image quality settings
  int _defaultImageQuality = 85;
  int _maxImageSize = 4096;

  // Processing settings
  bool _parallelProcessing = true;
  bool _memoryOptimization = true;

  // UI preferences
  bool _showProcessingProgress = true;
  bool _autoSaveEnabled = true;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get defaultImageQuality => _defaultImageQuality;
  int get maxImageSize => _maxImageSize;
  bool get parallelProcessing => _parallelProcessing;
  bool get memoryOptimization => _memoryOptimization;
  bool get showProcessingProgress => _showProcessingProgress;
  bool get autoSaveEnabled => _autoSaveEnabled;

  /// Initialize settings controller
  Future<void> initialize() async {
    _setLoading(true);
    _clearError();

    try {
      await _loadSettings();
    } catch (e) {
      _setError('Failed to load settings: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load settings from storage
  Future<void> _loadSettings() async {
    _defaultImageQuality = _storageService.getDefaultImageQuality();
    _maxImageSize = _storageService.getInt(StorageKeys.maxImageSize) ?? 4096;
    _parallelProcessing =
        _storageService.getBool(StorageKeys.parallelProcessing) ?? true;
    _memoryOptimization =
        _storageService.getBool(StorageKeys.memoryOptimization) ?? true;
    _autoSaveEnabled =
        _storageService.getBool(StorageKeys.autoSaveEnabled) ?? true;

    notifyListeners();
  }

  /// Set default image quality
  Future<void> setDefaultImageQuality(int quality) async {
    if (_defaultImageQuality == quality) return;

    _defaultImageQuality = quality;
    await _storageService.setDefaultImageQuality(quality);
    notifyListeners();
  }

  /// Set maximum image size
  Future<void> setMaxImageSize(int size) async {
    if (_maxImageSize == size) return;

    _maxImageSize = size;
    await _storageService.setInt(StorageKeys.maxImageSize, size);
    notifyListeners();
  }

  /// Set parallel processing
  Future<void> setParallelProcessing(bool enabled) async {
    if (_parallelProcessing == enabled) return;

    _parallelProcessing = enabled;
    await _storageService.setBool(StorageKeys.parallelProcessing, enabled);
    notifyListeners();
  }

  /// Set memory optimization
  Future<void> setMemoryOptimization(bool enabled) async {
    if (_memoryOptimization == enabled) return;

    _memoryOptimization = enabled;
    await _storageService.setBool(StorageKeys.memoryOptimization, enabled);
    notifyListeners();
  }

  /// Set auto save enabled
  Future<void> setAutoSaveEnabled(bool enabled) async {
    if (_autoSaveEnabled == enabled) return;

    _autoSaveEnabled = enabled;
    await _storageService.setBool(StorageKeys.autoSaveEnabled, enabled);
    notifyListeners();
  }

  /// Reset all settings to defaults
  Future<void> resetToDefaults() async {
    _setLoading(true);
    _clearError();

    try {
      // Remove all settings from storage
      await _storageService.remove(StorageKeys.defaultImageQuality);
      await _storageService.remove(StorageKeys.maxImageSize);
      await _storageService.remove(StorageKeys.parallelProcessing);
      await _storageService.remove(StorageKeys.memoryOptimization);
      await _storageService.remove(StorageKeys.autoSaveEnabled);

      // Reset to default values
      _defaultImageQuality = 85;
      _maxImageSize = 4096;
      _parallelProcessing = true;
      _memoryOptimization = true;
      _autoSaveEnabled = true;

      notifyListeners();
    } catch (e) {
      _setError('Failed to reset settings: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get available quality presets
  List<QualityPreset> getQualityPresets() {
    return [
      QualityPreset(name: 'Low', quality: 60, description: 'Smaller file size'),
      QualityPreset(
        name: 'Medium',
        quality: 75,
        description: 'Balanced quality and size',
      ),
      QualityPreset(
        name: 'High',
        quality: 85,
        description: 'Good quality (recommended)',
      ),
      QualityPreset(
        name: 'Maximum',
        quality: 95,
        description: 'Best quality, larger files',
      ),
    ];
  }

  /// Get available size presets
  List<SizePreset> getSizePresets() {
    return [
      SizePreset(name: 'Small', size: 1024, description: '1024px max'),
      SizePreset(name: 'Medium', size: 2048, description: '2048px max'),
      SizePreset(
        name: 'Large',
        size: 4096,
        description: '4096px max (recommended)',
      ),
      SizePreset(name: 'Original', size: 8192, description: 'No size limit'),
    ];
  }

  /// Dispose resources
  @override
  Future<void> dispose() async {
    super.dispose();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading == loading) return;
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    if (_error == error) return;
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _setError(null);
  }
}

/// Quality preset model
class QualityPreset {
  final String name;
  final int quality;
  final String description;

  QualityPreset({
    required this.name,
    required this.quality,
    required this.description,
  });
}

/// Size preset model
class SizePreset {
  final String name;
  final int size;
  final String description;

  SizePreset({
    required this.name,
    required this.size,
    required this.description,
  });
}
